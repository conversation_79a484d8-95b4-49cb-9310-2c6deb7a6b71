../../Scripts/mammoth.exe,sha256=945_sMoe7XS_C0LUYxU_hBZ1KD_8Y-49plK4200aWMc,108438
mammoth-1.6.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
mammoth-1.6.0.dist-info/LICENSE,sha256=ZmO70EkgXTiklsystBKhUZgLREYn043iGLO4Ca7zMPE,1307
mammoth-1.6.0.dist-info/METADATA,sha256=wbrh_u_xXuMlnGs5CIKPojAU7vyX8OG4uvtttxmsrJw,23638
mammoth-1.6.0.dist-info/RECORD,,
mammoth-1.6.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mammoth-1.6.0.dist-info/WHEEL,sha256=a-zpFRIJzOq5QfuhBzbhiA1eHTzNCJn8OdRvhdNX0Rk,110
mammoth-1.6.0.dist-info/entry_points.txt,sha256=uvuGZjpkv3IX-Gc39mGuq-mrEyuKg9DKx20yeaeZE3c,45
mammoth-1.6.0.dist-info/top_level.txt,sha256=bzpnNCrDpj3-sVobchossMHbU2p4dM4HboFD1chO_Go,8
mammoth/__init__.py,sha256=8O-rdzMb7O_iSnz4l3U-cr3AMAv9-y_9QVPTe_gRf_g,1463
mammoth/__pycache__/__init__.cpython-312.pyc,,
mammoth/__pycache__/cli.cpython-312.pyc,,
mammoth/__pycache__/conversion.cpython-312.pyc,,
mammoth/__pycache__/document_matchers.cpython-312.pyc,,
mammoth/__pycache__/documents.cpython-312.pyc,,
mammoth/__pycache__/html_paths.cpython-312.pyc,,
mammoth/__pycache__/images.cpython-312.pyc,,
mammoth/__pycache__/lists.cpython-312.pyc,,
mammoth/__pycache__/options.cpython-312.pyc,,
mammoth/__pycache__/raw_text.cpython-312.pyc,,
mammoth/__pycache__/results.cpython-312.pyc,,
mammoth/__pycache__/transforms.cpython-312.pyc,,
mammoth/__pycache__/underline.cpython-312.pyc,,
mammoth/__pycache__/zips.cpython-312.pyc,,
mammoth/cli.py,sha256=L6ZNOsYNESxaDZtvHLNH7heSv3haKPN1VieLGV7xgo0,3095
mammoth/conversion.py,sha256=E4cHTBjakAFXqv3Ys--x7Hf_dNdnl2EYI0JUj_Ew254,13513
mammoth/document_matchers.py,sha256=TYxpIkFCknPoaGbfI-BueKzPjtigm0XFUGcE1z7hnAU,1864
mammoth/documents.py,sha256=P-vdGV2IqIHNaM0ZDEmS76RAXaEouHOz_xjwnYW_8Ts,6001
mammoth/docx/__init__.py,sha256=fM-NmZbrgC2lCwTwRs9m7JRpY2rRHNwXM3wtuuL_Vv4,6342
mammoth/docx/__pycache__/__init__.cpython-312.pyc,,
mammoth/docx/__pycache__/body_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/comments_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/complex_fields.cpython-312.pyc,,
mammoth/docx/__pycache__/content_types_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/dingbats.cpython-312.pyc,,
mammoth/docx/__pycache__/document_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/files.cpython-312.pyc,,
mammoth/docx/__pycache__/notes_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/numbering_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/office_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/relationships_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/style_map.cpython-312.pyc,,
mammoth/docx/__pycache__/styles_xml.cpython-312.pyc,,
mammoth/docx/__pycache__/uris.cpython-312.pyc,,
mammoth/docx/__pycache__/xmlparser.cpython-312.pyc,,
mammoth/docx/body_xml.py,sha256=KVU7N1ZirJnk2HT5u6ETvvle4q8mWGO6JCmSRRapa24,23159
mammoth/docx/comments_xml.py,sha256=tX-XJ0pLRqoYb6XpJmnv7-l1CeW65qiNZXVA2xXU_ZE,849
mammoth/docx/complex_fields.py,sha256=FO1w-quqyLHLm64LOyPFVICMUy8e12m4w41KBIFlh1w,181
mammoth/docx/content_types_xml.py,sha256=0k1SaG3dkm5e4996UqEjcz78a18ZxCWcm1q9AQo_ST4,1632
mammoth/docx/dingbats.py,sha256=fbU_Pgr-yAWQ3DRqkiIuweke5zUgRwClLFzp5hh6gtI,32817
mammoth/docx/document_xml.py,sha256=chdnmJOPIs5P938H9x5LxbbS_UohewohjtdhEV153W4,498
mammoth/docx/files.py,sha256=hSZJAT1bTSItuahAlK-8ml00mARG2qrM1797qLzP3qg,1029
mammoth/docx/notes_xml.py,sha256=wN4c8Td11z3m75XyRoGgFr2Hal730_YymRIeGQx9VR0,979
mammoth/docx/numbering_xml.py,sha256=IKsO8r2dS7ip9Lgk_50H19nGmRCWWPo5vwAOqNKuUNg,3704
mammoth/docx/office_xml.py,sha256=CgBmmFsF7XePcpsmCDnSKdcg26rFV8lw9klWrBCwZlI,1270
mammoth/docx/relationships_xml.py,sha256=5GBsm4F4nROj2KPirtaTlec9-CAintinyomiy0ORVSk,1194
mammoth/docx/style_map.py,sha256=3Dz1cMkwTTqvvp33AAIYTzwxuBALGfxTSN050CUYSr0,2514
mammoth/docx/styles_xml.py,sha256=GIzKAbCHOIb2FyaeF6FBPGdTgpybmdLpVKd0_azDw4o,2990
mammoth/docx/uris.py,sha256=3ebe1GJEr5VIxPmpCH-PJfrmhOj3XRrYYFYASnhDApw,289
mammoth/docx/xmlparser.py,sha256=HoQ-YLMZRHAK5Z6YMTGbxk6FUbKLVYbG3kpQWL4RVak,3140
mammoth/html/__init__.py,sha256=AIUT63690bLA55KYE3q-mXegENIC52x823xY6XEqzVE,3516
mammoth/html/__pycache__/__init__.cpython-312.pyc,,
mammoth/html/__pycache__/nodes.cpython-312.pyc,,
mammoth/html/nodes.py,sha256=_0n94j4UX-kSro724JgYVRoAhNkJ706v8e2gbyAnAks,1076
mammoth/html_paths.py,sha256=fQGLPVkZSnsa2Sy3mvBPVHs5W8eiG8P1cOO9JipIZLY,1159
mammoth/images.py,sha256=C-qAZeKolzhtfUyE2xZ1NEGpXX84vR5aw53a8HMBfns,638
mammoth/lists.py,sha256=cM5nJR0f9U0oQ7GOOVD5lAQ-MLyf0FLH-XLmELp3E1M,785
mammoth/options.py,sha256=7PWkCOAuo2CUpbj3RoMZ1nfhxcYJZxTpL8lVFFdyrVU,3111
mammoth/raw_text.py,sha256=HnqOLKVQhryHCrn5FO1nWP2BPm38a8k50B4qYgPQo80,435
mammoth/results.py,sha256=W4XUcfPkxIWBn-0-29ohQp_msSAvv7WDq4t_psd__l0,905
mammoth/styles/__init__.py,sha256=7ooo29U6IqSjlcnbo_aYguezbYEsAJGiTtOiUMX6hx8,184
mammoth/styles/__pycache__/__init__.cpython-312.pyc,,
mammoth/styles/parser/__init__.py,sha256=4mi7ZCbPGZENHa6WkgcprYtGEsGNpZq-taJ53s2IVsE,514
mammoth/styles/parser/__pycache__/__init__.cpython-312.pyc,,
mammoth/styles/parser/__pycache__/document_matcher_parser.cpython-312.pyc,,
mammoth/styles/parser/__pycache__/errors.cpython-312.pyc,,
mammoth/styles/parser/__pycache__/html_path_parser.cpython-312.pyc,,
mammoth/styles/parser/__pycache__/style_mapping_parser.cpython-312.pyc,,
mammoth/styles/parser/__pycache__/token_iterator.cpython-312.pyc,,
mammoth/styles/parser/__pycache__/token_parser.cpython-312.pyc,,
mammoth/styles/parser/__pycache__/tokeniser.cpython-312.pyc,,
mammoth/styles/parser/document_matcher_parser.py,sha256=DwTEOSHNNFxunvAuBedLggFZj_jzlqEr_gXnJbyKy7g,3868
mammoth/styles/parser/errors.py,sha256=bm58riBwVSBpPuqG7l42WcmmsRiJA6Pss6pTxukZ7v4,42
mammoth/styles/parser/html_path_parser.py,sha256=RubeB8c6TL70MepyKYHgssfYMGElhfjfaigCp_kOzFE,2034
mammoth/styles/parser/style_mapping_parser.py,sha256=-olAaSEIThPlct59AsBPGTr4DY5HChGDJv-UKMpmcuk,498
mammoth/styles/parser/token_iterator.py,sha256=Dh_3GQwL2cPmKz_W2zvOoFrxyPrymYqCT1mvwYyZe6I,1786
mammoth/styles/parser/token_parser.py,sha256=f0fClB75ZWgoUalugxE8yngtW5vFduI0-tq1zPiDo-E,793
mammoth/styles/parser/tokeniser.py,sha256=w2XBiGrwBzKVrlOyPi-s-PhtxjV7ojgKFe4OWSGzt6g,1652
mammoth/transforms.py,sha256=6_3KrjYgbj4aoSWJI6kQhwiYJv2zEkpXC-H6kgbG2RE,1416
mammoth/underline.py,sha256=Fo0sIWhT-Y_l-vhVO7CuYv930k4ZpaSIh15_fbFEvM4,171
mammoth/writers/__init__.py,sha256=0eUvliuUfAHuBZ60A-MKK5puPcWXU22ZvY8yiRCQ7qU,320
mammoth/writers/__pycache__/__init__.cpython-312.pyc,,
mammoth/writers/__pycache__/abc.cpython-312.pyc,,
mammoth/writers/__pycache__/html.cpython-312.pyc,,
mammoth/writers/__pycache__/markdown.cpython-312.pyc,,
mammoth/writers/abc.py,sha256=x-SeNYD8Ugr1fijb-j0lLnxuC5D7BR1L74qE-DuhSU0,554
mammoth/writers/html.py,sha256=fQjZoNHJauVIZwJdZ7R7m4rM48u3wABO989hbV011ZE,1183
mammoth/writers/markdown.py,sha256=imAq1GY3EUZ_e1ZvxbVflMlUYPENruJ07B6-0WhpPyo,5300
mammoth/zips.py,sha256=X-Alu6klsYOUhSdyimLPBE9hoxlw6BMno2aqv08ax9w,1802
