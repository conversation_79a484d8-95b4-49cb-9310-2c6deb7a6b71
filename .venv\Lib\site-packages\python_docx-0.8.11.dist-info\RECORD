docx/__init__.py,sha256=i1QCjXmmGnK6DLrBVvQYIet1VDCtre2wzJ915FLQWmI,1354
docx/__pycache__/__init__.cpython-312.pyc,,
docx/__pycache__/api.cpython-312.pyc,,
docx/__pycache__/blkcntnr.cpython-312.pyc,,
docx/__pycache__/compat.cpython-312.pyc,,
docx/__pycache__/document.cpython-312.pyc,,
docx/__pycache__/exceptions.cpython-312.pyc,,
docx/__pycache__/package.cpython-312.pyc,,
docx/__pycache__/section.cpython-312.pyc,,
docx/__pycache__/settings.cpython-312.pyc,,
docx/__pycache__/shape.cpython-312.pyc,,
docx/__pycache__/shared.cpython-312.pyc,,
docx/__pycache__/table.cpython-312.pyc,,
docx/api.py,sha256=r_Xt4-sLyt9GbkzBVNkM2csj7JEW8pnIFLhNCpJbjg8,1179
docx/blkcntnr.py,sha256=CNZFXS-8IVQGdYLRy9mnuu5XxRDGy_tWbIYhPvodEMo,2511
docx/compat.py,sha256=ambZlSsRAuxlRc8fyzOjOjRxkpK7DaMhSMcUrWLCHvQ,1043
docx/dml/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docx/dml/__pycache__/__init__.cpython-312.pyc,,
docx/dml/__pycache__/color.cpython-312.pyc,,
docx/dml/color.py,sha256=MlLIhz--HOG7zfBY_M71BJkbMLfxTA39pIJjhh-2W4M,3978
docx/document.py,sha256=aZ85JlIa6CDO5G_OTLl24JSbxkrBXRtLHXEpf0kAELw,7497
docx/enum/__init__.py,sha256=NKqr4A6iE_jqXannH-OjmvwT4HtHxRtaPF1-EzQBa0g,343
docx/enum/__pycache__/__init__.cpython-312.pyc,,
docx/enum/__pycache__/base.cpython-312.pyc,,
docx/enum/__pycache__/dml.cpython-312.pyc,,
docx/enum/__pycache__/section.cpython-312.pyc,,
docx/enum/__pycache__/shape.cpython-312.pyc,,
docx/enum/__pycache__/style.cpython-312.pyc,,
docx/enum/__pycache__/table.cpython-312.pyc,,
docx/enum/__pycache__/text.cpython-312.pyc,,
docx/enum/base.py,sha256=3FYCvla_n4ZB_UZTTG7Huf36X_uHcQuUKMLj0qwlusc,11012
docx/enum/dml.py,sha256=I8p8eKxTmHrC287LTRfku1mR99e58H9JLrI2FSGXo48,3533
docx/enum/section.py,sha256=sKF1_RVwt0wBAZmjj6re6w4JXSMeLtrvryj5SEgU0xU,2666
docx/enum/shape.py,sha256=x2ApS_7UZDbRM9mK7XLnwLiF6dXxTKqIRCBGTYTs3jY,475
docx/enum/style.py,sha256=zDz2HLsljg1N8ZupjR_gH0yS0iWB5LcNrF6mHPACW7k,12189
docx/enum/table.py,sha256=pFclMm7bXBwrAfP79LCNl6VUepXGy4ruMtxGYRTcT4w,3929
docx/enum/text.py,sha256=vPOOPL4fegkREpAwnU8y6YBSCxeyviArir_7p-xxM68,10689
docx/exceptions.py,sha256=WXWTyHr0GansSnVgddA4IfdnTKV7SXBFK0Fgni80QXE,503
docx/image/__init__.py,sha256=qT4LjYBsvWq1Wco4E-sXRDMAd0kXad-eJPcOHV16A0Q,754
docx/image/__pycache__/__init__.cpython-312.pyc,,
docx/image/__pycache__/bmp.cpython-312.pyc,,
docx/image/__pycache__/constants.cpython-312.pyc,,
docx/image/__pycache__/exceptions.cpython-312.pyc,,
docx/image/__pycache__/gif.cpython-312.pyc,,
docx/image/__pycache__/helpers.cpython-312.pyc,,
docx/image/__pycache__/image.cpython-312.pyc,,
docx/image/__pycache__/jpeg.cpython-312.pyc,,
docx/image/__pycache__/png.cpython-312.pyc,,
docx/image/__pycache__/tiff.cpython-312.pyc,,
docx/image/bmp.py,sha256=bJLewB2drSvj240cudPYzrGSJ7EpmB1yXnj52h3oSkw,1512
docx/image/constants.py,sha256=i_ESAhffZuXga7mjeaFTlXX8FvrrbRT47D49YI6nPio,3471
docx/image/exceptions.py,sha256=fXgy9u7920wAviYG1p04i7ULe7-R3h_FC7yS5XYH4zc,432
docx/image/gif.py,sha256=cj-qXfXC7jHkHbIyv37rSy3awwm9XtG21uoCSn4lhW0,1260
docx/image/helpers.py,sha256=gsJ7tk_sGUvemE0J5aDdlwL4ydoH4AjN_BqomN5RtnA,3286
docx/image/image.py,sha256=gENqBGZYczpfY-vapXfjPEFJDcKqw0aQfR2K1DRvRUQ,8029
docx/image/jpeg.py,sha256=Ug70UINbGTrGprDjJffRGAWi0j-Kjn9JX6Ntp5KAWFE,16099
docx/image/png.py,sha256=N5TZGfaipQr_7MTOxl789f8O4FUhozh-lnfv1si6sSw,8789
docx/image/tiff.py,sha256=gs0UnZTSlC5X3aEALU_zN-Ab-e48hsn082a-RoSEmyA,11140
docx/opc/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docx/opc/__pycache__/__init__.cpython-312.pyc,,
docx/opc/__pycache__/compat.cpython-312.pyc,,
docx/opc/__pycache__/constants.cpython-312.pyc,,
docx/opc/__pycache__/coreprops.cpython-312.pyc,,
docx/opc/__pycache__/exceptions.cpython-312.pyc,,
docx/opc/__pycache__/oxml.cpython-312.pyc,,
docx/opc/__pycache__/package.cpython-312.pyc,,
docx/opc/__pycache__/packuri.cpython-312.pyc,,
docx/opc/__pycache__/part.cpython-312.pyc,,
docx/opc/__pycache__/phys_pkg.cpython-312.pyc,,
docx/opc/__pycache__/pkgreader.cpython-312.pyc,,
docx/opc/__pycache__/pkgwriter.cpython-312.pyc,,
docx/opc/__pycache__/rel.cpython-312.pyc,,
docx/opc/__pycache__/shared.cpython-312.pyc,,
docx/opc/__pycache__/spec.cpython-312.pyc,,
docx/opc/compat.py,sha256=pO9HYOGIsMfQHasNGS22LngA7LqsvtdxM2vibRBMcMU,1327
docx/opc/constants.py,sha256=6YLm2eQcED5OweNrcaD0mk-DkyU03giv2zCAkZByY48,20289
docx/opc/coreprops.py,sha256=7GFsuzH5K3vL0OlXLMhuZu_hpBNALlfukbYLD9wZv1M,3246
docx/opc/exceptions.py,sha256=Ox7Y2hHFB-iwENH6YferKJP3t8kwNYmvmyqmZP1ePzc,302
docx/opc/oxml.py,sha256=EKoIQEbw4l9hg27QZ8xPIyc5Y0tXeNWoSEBUGKKo6cM,8540
docx/opc/package.py,sha256=C5Wn8AEu_XRv3n3UlFPpHxI3z9LD5nOckscxIE7FBBQ,8510
docx/opc/packuri.py,sha256=ukAvUU30G5ekiifWVZOkUp1HllGCzcWfvTcqPIOySzk,3880
docx/opc/part.py,sha256=Oy_AeubNl9saJpIibqDdqUwIIlVm92b2JHVzjYJO0Wg,8157
docx/opc/parts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docx/opc/parts/__pycache__/__init__.cpython-312.pyc,,
docx/opc/parts/__pycache__/coreprops.cpython-312.pyc,,
docx/opc/parts/coreprops.py,sha256=3x_tOiEyRahBhlljHmPyZeKyS83VWiA0xv6cm0OSjwo,1664
docx/opc/phys_pkg.py,sha256=Xm9fVh6tBWdm8704czTkzgCtgPGvoMPz_5IoM1fajAU,4370
docx/opc/pkgreader.py,sha256=1S8bKf3yjCS7TKxNEl0MpkMVkH25u8lBO6ntlDKUzOs,10107
docx/opc/pkgwriter.py,sha256=cW1_qdD20pcdGY4YsD-Aes141pFvBjgt2mtSlNuw5uA,4524
docx/opc/rel.py,sha256=5btek39L2TzwbCl4I67uR3i_qa_jIE7SfTMdFg1JzJ0,5385
docx/opc/shared.py,sha256=2scKXZLoHtRjv49RZupGFEQloz1euSpiyp84dIe1-hI,1453
docx/opc/spec.py,sha256=FvtolRqGkbezB7_L_YEONoDG7avlvZXCJSFrgJh7diQ,716
docx/oxml/__init__.py,sha256=gdQ-fzWwU7JvOA58P6w_8qNB12f4pOzMjC7DEY55H5Q,9027
docx/oxml/__pycache__/__init__.cpython-312.pyc,,
docx/oxml/__pycache__/coreprops.cpython-312.pyc,,
docx/oxml/__pycache__/document.cpython-312.pyc,,
docx/oxml/__pycache__/exceptions.cpython-312.pyc,,
docx/oxml/__pycache__/ns.cpython-312.pyc,,
docx/oxml/__pycache__/numbering.cpython-312.pyc,,
docx/oxml/__pycache__/section.cpython-312.pyc,,
docx/oxml/__pycache__/settings.cpython-312.pyc,,
docx/oxml/__pycache__/shape.cpython-312.pyc,,
docx/oxml/__pycache__/shared.cpython-312.pyc,,
docx/oxml/__pycache__/simpletypes.cpython-312.pyc,,
docx/oxml/__pycache__/styles.cpython-312.pyc,,
docx/oxml/__pycache__/table.cpython-312.pyc,,
docx/oxml/__pycache__/xmlchemy.cpython-312.pyc,,
docx/oxml/coreprops.py,sha256=4FWeFJmiZG-WzulFIBwt-kRmtNlal_pv2ojGlp8IJjo,10278
docx/oxml/document.py,sha256=q3q_ix3Hsir2jriwEBZrO7fqwOrnx9-0jVYR6Q3KubQ,2513
docx/oxml/exceptions.py,sha256=nTJCycptQiR3mbhsDO1zM3rMu-5JYiVbXDflH0LlfNM,290
docx/oxml/ns.py,sha256=asuyoU2b3PmoY-q3DZ8sI08gbVuff5as_Z5yjc9tP9U,3775
docx/oxml/numbering.py,sha256=cuYvMr7FQYeI4tJV13qsOR_bE891-KuwZQ_daRIe_zQ,4119
docx/oxml/section.py,sha256=5QchDgdZJ-pNfWoXG51LtNA9Q64M3JlOp3hHMxfAmLM,11114
docx/oxml/settings.py,sha256=GsRhrCL458VaMlCO-fr8TNCHoaGNz_m7hy5ZKra09Bc,3491
docx/oxml/shape.py,sha256=eiQ-lmtXMqNIcQTzoJgkW44ZG_-GnadnkURlsSW5cEA,7783
docx/oxml/shared.py,sha256=wxij74zfPloaoFghQXSXyGq1BER_aoRyR0oQ2EV72Lw,1623
docx/oxml/simpletypes.py,sha256=o9EqP5kuebCWkDUfWmKYCys0Be4Heqvpy3uvGhI61bo,10225
docx/oxml/styles.py,sha256=nEk5HaDnI1AZ4w5ZnRbdVgCxQC9vyex9F4jDg-pOf4M,10886
docx/oxml/table.py,sha256=UeJK_MH6kjFUiJhsigs-JLWq1OMoPUlWw3dRlR-Ey70,27716
docx/oxml/text/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docx/oxml/text/__pycache__/__init__.cpython-312.pyc,,
docx/oxml/text/__pycache__/font.cpython-312.pyc,,
docx/oxml/text/__pycache__/paragraph.cpython-312.pyc,,
docx/oxml/text/__pycache__/parfmt.cpython-312.pyc,,
docx/oxml/text/__pycache__/run.cpython-312.pyc,,
docx/oxml/text/font.py,sha256=d-mu2MxkPy0jOlSnJy80D4BnDsp8UHm7qvmqwTO2toI,10114
docx/oxml/text/paragraph.py,sha256=4xYuQJwMxEtT5sgp-iUqEPbgI6YcBBU8qPRspaY6v1E,1908
docx/oxml/text/parfmt.py,sha256=QdKf9ZutqiTyBWksr82bSejYZOgBFc-N0QAx76UfD6o,10511
docx/oxml/text/run.py,sha256=Iz--Uvq80TuqMtcjUq5DTv0rNzl1BSaPM994v8D9S-w,4888
docx/oxml/xmlchemy.py,sha256=RYYxzjjdlUgn5LyEhe3ydrqth-1Jz8Dc-KdQdQEkNQE,24645
docx/package.py,sha256=d51lKQSFsovvdOHLqvW5X1CN47ugvNTOcJL66x7NF6Q,3868
docx/parts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docx/parts/__pycache__/__init__.cpython-312.pyc,,
docx/parts/__pycache__/document.cpython-312.pyc,,
docx/parts/__pycache__/hdrftr.cpython-312.pyc,,
docx/parts/__pycache__/image.cpython-312.pyc,,
docx/parts/__pycache__/numbering.cpython-312.pyc,,
docx/parts/__pycache__/settings.cpython-312.pyc,,
docx/parts/__pycache__/story.cpython-312.pyc,,
docx/parts/__pycache__/styles.cpython-312.pyc,,
docx/parts/document.py,sha256=F4cTUpg2GVZ9gaGyP0XhpmLKzNpxbZD6aHE7TBwJ5hM,5325
docx/parts/hdrftr.py,sha256=2W_pPpgw4jufm8LQidNcNy359AiCRVSOScrcXi4dj70,1717
docx/parts/image.py,sha256=M4_vgt37s5MhrqbFG88XExWpTFrbWPtO5GZdP7LtwHs,2668
docx/parts/numbering.py,sha256=6GoeOzmDjm7JFaXyPe5UuQNDgKXON4iT-DTJ_AaeHog,1230
docx/parts/settings.py,sha256=2lVFQyflhTR_k_7q3en058poaCjFN_DuQV3Y82Wpu7Y,1465
docx/parts/story.py,sha256=SYOI-z-yrqlUs4P_1UPQccl_7ZKOiF9LtVRtrF3Ar-s,3238
docx/parts/styles.py,sha256=gXfNsvQq9gfDncgLkZVMyKQuIGmFmvJLbdq4to_zxaw,1428
docx/section.py,sha256=Fh7ig-PcWLeDEahRhTcClmdpPGDEQJz6rnIJzGlNqDg,16201
docx/settings.py,sha256=lhLfrj4U4oIc9FmpvuLQ_TVyPOiHm4Knai186n5nNuM,780
docx/shape.py,sha256=kkBSSLtBJ7JnXhCMb6ykEfFAdT4iBg4uOhAAWnoBf6A,2811
docx/shared.py,sha256=qBRbxuo84Vm7AASBASiUDtS10JdOAXf81pgOC52JSwI,6740
docx/styles/__init__.py,sha256=2SK7eMTF_QsmoFT8SnTUZ0lxBIoxb3NnE_31_nt5h5Q,1500
docx/styles/__pycache__/__init__.cpython-312.pyc,,
docx/styles/__pycache__/latent.cpython-312.pyc,,
docx/styles/__pycache__/style.cpython-312.pyc,,
docx/styles/__pycache__/styles.cpython-312.pyc,,
docx/styles/latent.py,sha256=P_uXdRzMssa55HIwxcmse5x9kKE2h9oWQ53K8MA4xpA,7549
docx/styles/style.py,sha256=PM6irllSoyvbt3Mpd2Ex2hgha6sd6KVD3ATLLtT8z40,7987
docx/styles/styles.py,sha256=4nddHsiH4r0z3JBC16u8v80x6yAeEIxJJLiwhNzVNiQ,5637
docx/table.py,sha256=IgToLqJj0eYHkXmJI5igOtEg85sWhvbW_yQ5dW4PXTk,14129
docx/templates/default-footer.xml,sha256=V9Gkf2J3fxHVNphYHX2FfxuSdHUh1_tArHv1ZlUHAxU,1395
docx/templates/default-header.xml,sha256=oMHJ5mr6Sd1PZkmEQEJ4lmr7VCwZiFOAvmJSsOKK4UI,1395
docx/templates/default-settings.xml,sha256=eXOLlF4o4eqDOLeEi5qjwTv5mmDk3V5CMseQD7_bCug,1640
docx/templates/default-styles.xml,sha256=WnjtOZLsCnP4hYSaYhMfR7JW4ixDxRcr3eBPI8QRx94,15823
docx/templates/default.docx,sha256=IJS1vd_-nPlz1h_gM4hBOATwNBYHGElKZdt-mNpA010,38116
docx/text/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
docx/text/__pycache__/__init__.cpython-312.pyc,,
docx/text/__pycache__/font.cpython-312.pyc,,
docx/text/__pycache__/paragraph.cpython-312.pyc,,
docx/text/__pycache__/parfmt.cpython-312.pyc,,
docx/text/__pycache__/run.cpython-312.pyc,,
docx/text/__pycache__/tabstops.cpython-312.pyc,,
docx/text/font.py,sha256=YB3vg414eXI0_MFkjZAkKri8XKaDm52A7IacyhIDML0,11998
docx/text/paragraph.py,sha256=jPK2F2ojZh4Ab-th5YAs1MNx7-YnSA-IEMqBvSmvDno,4813
docx/text/parfmt.py,sha256=zG-y_IdLKZcX1IZMQI3-hcPlzOanZYa8HkUPiSulONU,10636
docx/text/run.py,sha256=3ZZLXMHZvNNQ3A5oIlmBN8tQdgoJK1TiCP5BpoZ6sT0,6988
docx/text/tabstops.py,sha256=wHS3cutWktx6nLweEh-qNc5pBNDDaQGGfmVpRU-VHFI,4183
python_docx-0.8.11.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
python_docx-0.8.11.dist-info/METADATA,sha256=zoGJrfDwHRnDh-s37ItBNbMg7MpL0sN_t8vn0SEyemQ,9439
python_docx-0.8.11.dist-info/RECORD,,
python_docx-0.8.11.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
python_docx-0.8.11.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
python_docx-0.8.11.dist-info/licenses/LICENSE,sha256=dlLyceRtDVM-ncRj87X8vcrPTWqcjWtVTRXv0PN_YTI,1104
python_docx-0.8.11.dist-info/top_level.txt,sha256=zkbrD3gmyqHpD_XhHe7lCbr2M5vT229nJ4UFqF5VjIs,5
