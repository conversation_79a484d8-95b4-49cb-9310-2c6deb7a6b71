# Talent Hero Evaluator - Executable Version

## Quick Start

1. **Download and Extract**: Extract all files to a folder
2. **Run the Application**: Double-click `TalentHeroEvaluator.exe`
3. **Ensure Ollama is Running**: Make sure Ollama is installed and running on localhost:11434

## Prerequisites

### Ollama Setup (Required)
The application requires Ollama to be installed and running:

1. **Download Ollama**: Visit https://ollama.ai and download for your OS
2. **Install Ollama**: Follow the installation instructions
3. **Start Ollama**: Run `ollama serve` in command prompt/terminal
4. **Install Models**: Download at least one model, for example:
   ```
   ollama pull llama2
   ollama pull mistral
   ollama pull cogito:3b
   ```

## Usage

1. **Start Ollama**: Ensure Ollama is running (`ollama serve`)
2. **Run Application**: Double-click `TalentHeroEvaluator.exe`
3. **Upload Files**: 
   - Upload one job description PDF
   - Upload multiple resume PDFs
4. **Select Model**: Choose from available Ollama models
5. **Configure Parameters**: Select 5-10 evaluation criteria
6. **Run Evaluation**: Click "Start Evaluation"
7. **View Results**: See ranked resumes and download/export results

## Troubleshooting

### "Cannot connect to Ollama"
- Ensure Ollama is running: `ollama serve`
- Check if models are available: `ollama list`
- Verify Ollama is on port 11434

### Application Won't Start
- Check Windows Defender/Antivirus (may block unsigned executables)
- Run as Administrator if needed
- Ensure all files are extracted together

### Slow Performance
- Large PDF files take longer to process
- Complex models may be slower
- Consider using smaller/faster models for quick testing

## File Structure
```
TalentHeroEvaluator/
├── TalentHeroEvaluator.exe    # Main application
├── README_EXECUTABLE.txt      # This file
└── [other bundled files]      # Required dependencies
```

## Support

For issues:
1. Ensure Ollama is properly installed and running
2. Check that you have at least one model downloaded
3. Verify PDF files are not corrupted or encrypted
4. Try running with different Ollama models

## System Requirements

- Windows 10/11 (64-bit)
- 4GB RAM minimum (8GB recommended)
- 500MB free disk space
- Ollama installed and running
- Internet connection (for Ollama model downloads)