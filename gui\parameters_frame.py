"""
Parameters selection frame for evaluation criteria
"""
import tkinter as tk
from tkinter import ttk, messagebox
from config.settings import DEFAULT_EVALUATION_PARAMETERS


class ParametersFrame(ttk.Frame):
    """
    Frame for selecting evaluation parameters
    """
    
    def __init__(self, parent):
        super().__init__(parent)
        self.parameter_vars = {}
        self.custom_parameters = []
        
        self.create_widgets()
        self.load_default_parameters()
    
    def create_widgets(self):
        """
        Create and layout widgets
        """
        # Main frame
        main_frame = ttk.LabelFrame(self, text="Evaluation Parameters", padding="10")
        main_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Instructions
        instructions = ttk.Label(
            main_frame, 
            text="Select 5-10 parameters to evaluate resumes against the job description:",
            font=("TkDefaultFont", 9)
        )
        instructions.pack(anchor="w", pady=(0, 10))
        
        # Parameters container with scrollbar
        container_frame = ttk.Frame(main_frame)
        container_frame.pack(fill="both", expand=True)
        
        # Canvas and scrollbar for scrollable parameters
        canvas = tk.Canvas(container_frame, height=200)
        scrollbar = ttk.Scrollbar(container_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Custom parameter section
        custom_frame = ttk.Frame(main_frame)
        custom_frame.pack(fill="x", pady=(10, 0))
        
        ttk.Label(custom_frame, text="Add custom parameter:").pack(anchor="w")
        
        custom_input_frame = ttk.Frame(custom_frame)
        custom_input_frame.pack(fill="x", pady=5)
        
        self.custom_entry = ttk.Entry(custom_input_frame)
        self.custom_entry.pack(side="left", fill="x", expand=True)
        
        self.add_custom_button = ttk.Button(
            custom_input_frame,
            text="Add",
            command=self.add_custom_parameter
        )
        self.add_custom_button.pack(side="right", padx=(5, 0))
        
        # Bind Enter key to add custom parameter
        self.custom_entry.bind("<Return>", lambda e: self.add_custom_parameter())
        
        # Selection info
        self.selection_info = ttk.Label(main_frame, text="0 parameters selected")
        self.selection_info.pack(anchor="w", pady=(10, 0))
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill="x", pady=(10, 0))
        
        self.select_all_button = ttk.Button(
            buttons_frame,
            text="Select All",
            command=self.select_all_parameters
        )
        self.select_all_button.pack(side="left")
        
        self.clear_all_button = ttk.Button(
            buttons_frame,
            text="Clear All",
            command=self.clear_all_parameters
        )
        self.clear_all_button.pack(side="left", padx=(5, 0))
        
        self.select_default_button = ttk.Button(
            buttons_frame,
            text="Select Default (5)",
            command=self.select_default_parameters
        )
        self.select_default_button.pack(side="left", padx=(5, 0))
    
    def load_default_parameters(self):
        """
        Load default evaluation parameters
        """
        for param in DEFAULT_EVALUATION_PARAMETERS:
            self.add_parameter_checkbox(param, is_default=True)
        
        self.update_selection_info()
    
    def add_parameter_checkbox(self, parameter_name, is_default=False, selected=False):
        """
        Add a parameter checkbox to the scrollable frame
        """
        if parameter_name in self.parameter_vars:
            return  # Parameter already exists
        
        var = tk.BooleanVar(value=selected)
        var.trace("w", lambda *args: self.update_selection_info())
        
        frame = ttk.Frame(self.scrollable_frame)
        frame.pack(fill="x", pady=1)
        
        checkbox = ttk.Checkbutton(
            frame,
            text=parameter_name,
            variable=var
        )
        checkbox.pack(side="left")
        
        # Add remove button for custom parameters
        if not is_default:
            remove_button = ttk.Button(
                frame,
                text="×",
                width=3,
                command=lambda: self.remove_custom_parameter(parameter_name)
            )
            remove_button.pack(side="right")
        
        self.parameter_vars[parameter_name] = var
    
    def add_custom_parameter(self):
        """
        Add a custom evaluation parameter
        """
        parameter_name = self.custom_entry.get().strip()
        
        if not parameter_name:
            messagebox.showwarning("Invalid Parameter", "Please enter a parameter name.")
            return
        
        if parameter_name in self.parameter_vars:
            messagebox.showwarning("Duplicate Parameter", "This parameter already exists.")
            return
        
        if len(parameter_name) > 50:
            messagebox.showwarning("Parameter Too Long", "Parameter name must be 50 characters or less.")
            return
        
        self.add_parameter_checkbox(parameter_name, is_default=False, selected=True)
        self.custom_parameters.append(parameter_name)
        self.custom_entry.delete(0, tk.END)
        self.update_selection_info()
    
    def remove_custom_parameter(self, parameter_name):
        """
        Remove a custom parameter
        """
        if parameter_name in self.parameter_vars:
            # Find and destroy the frame containing this parameter
            for widget in self.scrollable_frame.winfo_children():
                if isinstance(widget, ttk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Checkbutton) and child.cget("text") == parameter_name:
                            widget.destroy()
                            break
            
            del self.parameter_vars[parameter_name]
            if parameter_name in self.custom_parameters:
                self.custom_parameters.remove(parameter_name)
            
            self.update_selection_info()
    
    def select_all_parameters(self):
        """
        Select all available parameters
        """
        for var in self.parameter_vars.values():
            var.set(True)
    
    def clear_all_parameters(self):
        """
        Clear all parameter selections
        """
        for var in self.parameter_vars.values():
            var.set(False)
    
    def select_default_parameters(self):
        """
        Select the first 5 default parameters
        """
        self.clear_all_parameters()
        
        count = 0
        for param_name in DEFAULT_EVALUATION_PARAMETERS:
            if param_name in self.parameter_vars and count < 5:
                self.parameter_vars[param_name].set(True)
                count += 1
    
    def update_selection_info(self):
        """
        Update the selection information label
        """
        selected_count = sum(1 for var in self.parameter_vars.values() if var.get())
        self.selection_info.config(text=f"{selected_count} parameters selected")
        
        # Update text color based on selection count
        if 5 <= selected_count <= 10:
            self.selection_info.config(foreground="green")
        elif selected_count > 10:
            self.selection_info.config(foreground="orange")
        else:
            self.selection_info.config(foreground="red")
    
    def get_selected_parameters(self):
        """
        Get list of selected parameters
        """
        selected = []
        for param_name, var in self.parameter_vars.items():
            if var.get():
                selected.append(param_name)
        return selected
    
    def is_valid_selection(self):
        """
        Check if the parameter selection is valid (5-10 parameters)
        """
        selected_count = len(self.get_selected_parameters())
        return 5 <= selected_count <= 10
