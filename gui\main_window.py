"""
Main window for Talent Hero Evaluator
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading
from config.settings import APP_TITLE, WINDOW_WIDTH, WINDOW_HEIGHT
from gui.file_upload_frame import FileUploadFrame
from gui.parameters_frame import ParametersFrame
from gui.results_frame import ResultsFrame
from gui.model_selection_frame import ModelSelectionFrame
from core.evaluator import ResumeEvaluator
from gui.key_management_frame import KeyManagementFrame

class MainWindow:
    """
    Main application window
    """
    
    def __init__(self):
        self.root = tk.Tk()
        self.evaluator = ResumeEvaluator()
        self.evaluation_thread = None
        
        self.setup_window()
        self.create_widgets()
        self.create_menu()
    
    def setup_window(self):
        """
        Setup main window properties
        """
        self.root.title(APP_TITLE)
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        self.root.minsize(800, 600)
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (WINDOW_WIDTH // 2)
        y = (self.root.winfo_screenheight() // 2) - (WINDOW_HEIGHT // 2)
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}+{x}+{y}")
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
    
    def create_widgets(self):
        """
        Create and layout main widgets
        """
        # Main container
        main_container = ttk.Frame(self.root)
        main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(
            main_container,
            text=APP_TITLE,
            font=("TkDefaultFont", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_container)
        self.notebook.pack(fill="both", expand=True)
        
        # Tab 1: File Upload
        self.file_upload_frame = FileUploadFrame(self.notebook)
        self.notebook.add(self.file_upload_frame, text="📁 Upload Files")

        # Tab 2: Model Selection
        self.model_selection_frame = ModelSelectionFrame(self.notebook, self.evaluator)
        self.notebook.add(self.model_selection_frame, text="🤖 Model")

        # Tab 3: Parameters
        self.parameters_frame = ParametersFrame(self.notebook)
        self.notebook.add(self.parameters_frame, text="⚙️ Parameters")

        # Tab 4: Results
        self.results_frame = ResultsFrame(self.notebook)
        self.notebook.add(self.results_frame, text="📊 Results")

        # Tab 5: Groq Key Management
        self.key_management_frame = KeyManagementFrame(self.notebook, self.evaluator.groq_client)
        self.notebook.add(self.key_management_frame, text=" Groq Keys")
        # Control buttons frame
        control_frame = ttk.Frame(main_container)
        control_frame.pack(fill="x", pady=(20, 0))
        
        # Progress bar
        self.progress_var = tk.StringVar(value="Ready to start evaluation")
        self.progress_label = ttk.Label(control_frame, textvariable=self.progress_var)
        self.progress_label.pack(anchor="w")
        
        self.progress_bar = ttk.Progressbar(
            control_frame,
            mode="determinate",
            length=400
        )
        self.progress_bar.pack(fill="x", pady=5)
        
        # Buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill="x", pady=(10, 0))
        
        self.evaluate_button = ttk.Button(
            button_frame,
            text="🚀 Start Evaluation",
            command=self.start_evaluation,
            style="Accent.TButton"
        )
        self.evaluate_button.pack(side="left")
        
        self.cancel_button = ttk.Button(
            button_frame,
            text="❌ Cancel",
            command=self.cancel_evaluation,
            state="disabled"
        )
        self.cancel_button.pack(side="left", padx=(10, 0))
        
        self.clear_button = ttk.Button(
            button_frame,
            text="🗑️ Clear All",
            command=self.clear_all_data
        )
        self.clear_button.pack(side="right")
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        self.status_bar = ttk.Label(
            main_container,
            textvariable=self.status_var,
            relief="sunken",
            anchor="w"
        )
        self.status_bar.pack(fill="x", side="bottom", pady=(10, 0))
    
    def create_menu(self):
        """
        Create application menu
        """
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Clear All Data", command=self.clear_all_data)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Test Ollama Connection", command=self.test_ollama_connection)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def start_evaluation(self):
        """
        Start the resume evaluation process
        """
        # Validate inputs
        if not self.file_upload_frame.is_ready_for_evaluation():
            messagebox.showerror(
                "Missing Files",
                "Please upload a job description and at least one resume file."
            )
            self.notebook.select(0)  # Switch to upload tab
            return
        
        if not self.model_selection_frame.is_model_available():
            messagebox.showerror(
                "No Model Selected",
                "Please select an available Ollama model for evaluation."
            )
            self.notebook.select(1)  # Switch to model tab
            return

        if not self.parameters_frame.is_valid_selection():
            messagebox.showerror(
                "Invalid Parameters",
                "Please select between 5 and 10 evaluation parameters."
            )
            self.notebook.select(2)  # Switch to parameters tab
            return
        
        # Get data
        job_description_path = self.file_upload_frame.get_job_description_path()
        resume_paths = self.file_upload_frame.get_resume_paths()
        parameters = self.parameters_frame.get_selected_parameters()
        
        # Update UI for evaluation
        self.evaluate_button.config(state="disabled")
        self.cancel_button.config(state="normal")
        self.progress_bar.config(value=0, maximum=len(resume_paths))
        self.status_var.set("Starting evaluation...")
        
        # Clear previous results
        self.results_frame.clear_results()
        
        # Start evaluation in separate thread
        self.evaluation_thread = threading.Thread(
            target=self.run_evaluation,
            args=(job_description_path, resume_paths, parameters),
            daemon=True
        )
        self.evaluation_thread.start()
    
    def run_evaluation(self, job_description_path, resume_paths, parameters):
        """
        Run evaluation with real-time updates
        """
        try:
            # Load job description
            self.update_progress(0, len(resume_paths), "Loading job description...")
            
            success, message = self.evaluator.load_job_description(job_description_path)
            if not success:
                self.evaluation_complete(False, message)
                return
            
            # Switch to results tab for real-time viewing
            self.root.after(0, lambda: self.notebook.select(3))
            
            # Evaluate resumes with real-time callback
            success, results = self.evaluator.evaluate_resumes(
                resume_paths,
                parameters,
                progress_callback=self.update_progress,
                result_callback=self.update_single_result
            )
            
            if success:
                self.evaluation_complete(True, results, parameters)
            else:
                self.evaluation_complete(False, results)
                
        except Exception as e:
            self.evaluation_complete(False, f"Unexpected error: {str(e)}")

    def update_single_result(self, result, current_count, total_count):
        """
        Update results display with single result
        """
        def update_ui():
            self.results_frame.add_single_result(result, current_count, total_count)
        
        self.root.after(0, update_ui)
    
    def update_progress(self, current, total, message):
        """
        Update progress bar and message (thread-safe)
        """
        def update_ui():
            self.progress_bar.config(value=current)
            self.progress_var.set(f"{message} ({current}/{total})")
            self.status_var.set(message)
        
        self.root.after(0, update_ui)
    
    def evaluation_complete(self, success, data, parameters=None):
        """
        Handle evaluation completion (thread-safe)
        """
        def update_ui():
            # Reset UI
            self.evaluate_button.config(state="normal")
            self.cancel_button.config(state="disabled")
            
            if success:
                # Display results
                self.results_frame.display_results(data, parameters)
                self.notebook.select(3)  # Switch to results tab
                
                successful_count = sum(1 for r in data if r['success'])
                self.progress_var.set(f"Evaluation complete! {successful_count}/{len(data)} resumes processed successfully.")
                self.status_var.set("Evaluation completed successfully")
                
                messagebox.showinfo(
                    "Evaluation Complete",
                    f"Successfully evaluated {successful_count} out of {len(data)} resumes.\n\nCheck the Results tab to view rankings."
                )
            else:
                # Show error
                self.progress_var.set("Evaluation failed")
                self.status_var.set("Evaluation failed")
                messagebox.showerror("Evaluation Failed", data)
        
        self.root.after(0, update_ui)
    
    def cancel_evaluation(self):
        """
        Cancel ongoing evaluation
        """
        # Note: This is a simple implementation. In a production app,
        # you might want to implement proper thread cancellation
        self.evaluate_button.config(state="normal")
        self.cancel_button.config(state="disabled")
        self.progress_var.set("Evaluation cancelled")
        self.status_var.set("Ready")
        
        messagebox.showinfo("Cancelled", "Evaluation has been cancelled.")
    
    def clear_all_data(self):
        """
        Clear all data and reset the application
        """
        result = messagebox.askyesno(
            "Clear All Data",
            "This will clear all uploaded files, parameters, and results. Are you sure?"
        )
        
        if result:
            # Clear file uploads
            self.file_upload_frame.job_description_path = None
            self.file_upload_frame.job_desc_label.config(text="No file selected", foreground="black")
            self.file_upload_frame.clear_resumes()
            
            # Clear model selection
            self.model_selection_frame.refresh_models()

            # Clear parameters
            self.parameters_frame.clear_all_parameters()

            # Clear results
            self.results_frame.clear_results()
            
            # Reset progress
            self.progress_bar.config(value=0)
            self.progress_var.set("Ready to start evaluation")
            self.status_var.set("Ready")
            
            # Switch to first tab
            self.notebook.select(0)
    
    def test_ollama_connection(self):
        """
        Test connection to Ollama
        """
        self.model_selection_frame.test_connection()
    
    def show_about(self):
        """
        Show about dialog
        """
        about_text = f"""
{APP_TITLE}

A powerful tool for evaluating resumes against job descriptions using AI.

Features:
• Upload job descriptions and multiple resumes
• Customizable evaluation parameters
• AI-powered scoring using Ollama
• Ranked results with downloadable resumes
• Export results to CSV

Powered by Ollama and the cogito:3b model.
        """
        
        messagebox.showinfo("About", about_text.strip())
    
    def run(self):
        """
        Start the application
        """
        self.root.mainloop()

