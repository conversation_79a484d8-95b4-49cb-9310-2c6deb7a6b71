"""
Results display frame for showing ranked resumes
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import shutil
from utils.file_utils import copy_file_to_downloads, sanitize_filename


class ResultsFrame(ttk.Frame):
    """
    Frame for displaying evaluation results and rankings
    """
    
    def __init__(self, parent):
        super().__init__(parent)
        self.results_data = []
        self.parameters = []
        
        self.create_widgets()
    
    def create_widgets(self):
        """
        Create and layout widgets
        """
        # Main frame
        main_frame = ttk.LabelFrame(self, text="Evaluation Results", padding="10")
        main_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Results info frame
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill="x", pady=(0, 10))
        
        self.results_info_label = ttk.Label(info_frame, text="No evaluation results yet")
        self.results_info_label.pack(side="left")
        
        # Export button
        self.export_button = ttk.Button(
            info_frame,
            text="Export to CSV",
            command=self.export_results,
            state="disabled"
        )
        self.export_button.pack(side="right", padx=(5, 0))
        
        # Treeview for results
        tree_frame = ttk.Frame(main_frame)
        tree_frame.pack(fill="both", expand=True)
        
        # Create treeview with scrollbars
        self.tree = ttk.Treeview(tree_frame, show="headings", height=15)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient="horizontal", command=self.tree.xview)

        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack scrollbars first, then treeview
        v_scrollbar.pack(side="right", fill="y")
        h_scrollbar.pack(side="bottom", fill="x")
        self.tree.pack(side="left", fill="both", expand=True)
        
        # Bind double-click event for downloading
        self.tree.bind("<Double-1>", self.on_resume_double_click)
        
        # Context menu
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="Download Resume", command=self.download_selected_resume)
        self.context_menu.add_command(label="View Details", command=self.view_resume_details)
        
        self.tree.bind("<Button-3>", self.show_context_menu)  # Right-click
        
        # Instructions label
        self.instructions_label = ttk.Label(
            main_frame,
            text="Double-click on a resume to download it, or right-click for more options.",
            font=("TkDefaultFont", 9),
            foreground="gray"
        )
        self.instructions_label.pack(pady=(10, 0))
    
    def setup_columns(self, parameters):
        """
        Setup treeview columns based on evaluation parameters
        """
        self.parameters = parameters
        
        # Define columns
        columns = ["Rank", "Resume", "Average Score"] + parameters + ["Comments"]
        self.tree["columns"] = columns
        
        # Configure column headings and widths
        self.tree.heading("Rank", text="Rank")
        self.tree.column("Rank", width=50, minwidth=50)
        
        self.tree.heading("Resume", text="Resume File")
        self.tree.column("Resume", width=200, minwidth=150)
        
        self.tree.heading("Average Score", text="Avg Score")
        self.tree.column("Average Score", width=80, minwidth=80)
        
        # Parameter columns
        for param in parameters:
            self.tree.heading(param, text=param)
            self.tree.column(param, width=100, minwidth=80)
        
        self.tree.heading("Comments", text="Comments")
        self.tree.column("Comments", width=300, minwidth=200)
    
    def add_single_result(self, result, current_count, total_count):
        """
        Add a single result and update rankings dynamically
        """
        # Add to results data
        self.results_data.append(result)
        
        # Re-sort and update display
        self.display_results(self.results_data, self.parameters)
        
        # Update progress info
        successful = sum(1 for r in self.results_data if r['success'])
        self.results_info_label.config(
            text=f"Evaluated {current_count}/{total_count} resumes ({successful} successful)"
        )

    def display_results(self, results_data, parameters):
        """
        Enhanced display with status indicators
        """
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Sort by average score (descending)
        sorted_results = sorted(results_data, key=lambda x: x.get('average_score', 0), reverse=True)
        
        for rank, result in enumerate(sorted_results, 1):
            # Status indicator
            status = result.get('status', 'unknown')
            if status == 'completed':
                status_icon = "✅"
            elif status == 'failed':
                status_icon = "❌"
            else:
                status_icon = "⏳"
            
            row_data = [
                rank,
                f"{status_icon} {result['file_name']}",
                f"{result['average_score']:.2f}",
                result['overall_comments'][:100] + "..." if len(result['overall_comments']) > 100 else result['overall_comments']
            ]
            
            # Add parameter scores
            for param in parameters:
                score = result.get('scores', {}).get(param, 0)
                row_data.append(f"{score:.2f}")
            
            item_id = self.tree.insert("", "end", values=row_data)
            
            # Color coding
            if result['success']:
                if result['average_score'] >= 8:
                    self.tree.set(item_id, "Average Score", f"⭐ {result['average_score']:.2f}")
                elif result['average_score'] >= 6:
                    self.tree.set(item_id, "Average Score", f"✓ {result['average_score']:.2f}")
            else:
                # Red background for failed evaluations
                self.tree.set(item_id, "File Name", f"❌ {result['file_name']}")
    
    def on_resume_double_click(self, event):
        """
        Handle double-click on resume item
        """
        try:
            print("DEBUG: Double-click event triggered")
            # Identify the item that was double-clicked
            item = self.tree.identify_row(event.y)
            print(f"DEBUG: Identified item: {item}")
            if item:
                # Select the item first
                self.tree.selection_set(item)
                print("DEBUG: Item selected, calling download_selected_resume")
                # Then download it
                self.download_selected_resume()
            else:
                print("DEBUG: No item identified at click position")
        except Exception as e:
            print(f"DEBUG: Error in double-click handler: {e}")
            messagebox.showerror("Debug Error", f"Double-click error: {str(e)}")
    
    def show_context_menu(self, event):
        """
        Show context menu on right-click
        """
        # Select the item under cursor
        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def download_selected_resume(self):
        """
        Download the selected resume file
        """
        try:
            print("DEBUG: download_selected_resume called")
            selection = self.tree.selection()
            print(f"DEBUG: Current selection: {selection}")

            if not selection:
                messagebox.showwarning("No Selection", "Please select a resume to download.")
                return

            # Get selected item data
            item = selection[0]
            values = self.tree.item(item, "values")
            print(f"DEBUG: Item values: {values}")

            if not values or len(values) < 2:
                messagebox.showerror("Error", f"Invalid selection data. Values: {values}")
                return

            resume_filename = values[1]  # Resume file name
            print(f"DEBUG: Resume filename: {resume_filename}")

            # Find the corresponding result data
            result_data = None
            print(f"DEBUG: Searching in {len(self.results_data)} results")
            for result in self.results_data:
                print(f"DEBUG: Checking result file_name: {result.get('file_name', 'NO_FILE_NAME')}")
                if result['file_name'] == resume_filename:
                    result_data = result
                    break

            if not result_data:
                messagebox.showerror("Error", f"Could not find resume data for: {resume_filename}")
                return

            print(f"DEBUG: Found result data with file_path: {result_data.get('file_path', 'NO_FILE_PATH')}")

            # Check if source file exists
            if 'file_path' not in result_data or not os.path.exists(result_data['file_path']):
                messagebox.showerror("Error", f"Source file not found:\n{result_data.get('file_path', 'No file path')}")
                return

            # Ask user where to save the file
            clean_filename = sanitize_filename(resume_filename)
            try:
                save_path = filedialog.asksaveasfilename(
                    title="Save Resume As",
                    filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                    initialname=clean_filename if clean_filename else "resume.pdf"
                )
            except Exception as dialog_error:
                print(f"DEBUG: File dialog error: {dialog_error}")
                # Fallback to simple dialog
                save_path = filedialog.asksaveasfilename(
                    title="Save Resume As"
                )

            if save_path:
                try:
                    shutil.copy2(result_data['file_path'], save_path)
                    messagebox.showinfo("Success", f"Resume downloaded successfully to:\n{save_path}")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to download resume:\n{str(e)}")
        except Exception as e:
            print(f"DEBUG: Error in download_selected_resume: {e}")
            messagebox.showerror("Debug Error", f"Download error: {str(e)}")
    
    def view_resume_details(self):
        """
        View detailed information about the selected resume
        """
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a resume to view details.")
            return
        
        # Get selected item data
        item = selection[0]
        values = self.tree.item(item, "values")
        
        if not values:
            return
        
        resume_filename = values[1]
        
        # Find the corresponding result data
        result_data = None
        for result in self.results_data:
            if result['file_name'] == resume_filename:
                result_data = result
                break
        
        if not result_data:
            messagebox.showerror("Error", "Could not find resume data.")
            return
        
        # Create details window
        self.show_details_window(result_data)
    
    def show_details_window(self, result_data):
        """
        Show detailed evaluation results in a new window
        """
        details_window = tk.Toplevel(self)
        details_window.title(f"Details - {result_data['file_name']}")
        details_window.geometry("600x500")
        details_window.resizable(True, True)
        
        # Main frame with scrollbar
        main_frame = ttk.Frame(details_window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Canvas and scrollbar for scrollable content
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # File info
        ttk.Label(scrollable_frame, text="File Information", font=("TkDefaultFont", 12, "bold")).pack(anchor="w", pady=(0, 5))
        ttk.Label(scrollable_frame, text=f"File Name: {result_data['file_name']}").pack(anchor="w")
        ttk.Label(scrollable_frame, text=f"Average Score: {result_data['average_score']:.2f}/10").pack(anchor="w")
        
        # Individual scores
        ttk.Label(scrollable_frame, text="Individual Scores", font=("TkDefaultFont", 12, "bold")).pack(anchor="w", pady=(20, 5))
        
        for param, score in result_data['scores'].items():
            score_text = f"{param}: {score:.2f}/10"
            ttk.Label(scrollable_frame, text=score_text).pack(anchor="w", padx=(20, 0))
        
        # Comments
        ttk.Label(scrollable_frame, text="Overall Comments", font=("TkDefaultFont", 12, "bold")).pack(anchor="w", pady=(20, 5))
        
        comments_text = tk.Text(scrollable_frame, height=8, wrap="word", state="normal")
        comments_text.insert("1.0", result_data['overall_comments'])
        comments_text.config(state="disabled")
        comments_text.pack(fill="x", pady=(0, 10))
        
        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Close button
        ttk.Button(details_window, text="Close", command=details_window.destroy).pack(pady=10)
    
    def export_results(self):
        """
        Export results to CSV file
        """
        try:
            print("DEBUG: export_results called")
            print(f"DEBUG: results_data length: {len(self.results_data)}")
            print(f"DEBUG: parameters: {self.parameters}")

            if not self.results_data:
                messagebox.showwarning("No Data", "No results to export.")
                return

            # Ask user where to save the CSV
            try:
                save_path = filedialog.asksaveasfilename(
                    title="Export Results to CSV",
                    filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                    initialname="resume_evaluation_results.csv"
                )
            except Exception as dialog_error:
                print(f"DEBUG: CSV dialog error: {dialog_error}")
                # Fallback to simple dialog
                save_path = filedialog.asksaveasfilename(
                    title="Export Results to CSV"
                )

            print(f"DEBUG: Save path selected: {save_path}")

            if not save_path:
                print("DEBUG: User cancelled save dialog")
                return  # User cancelled

            try:
                import csv

                # Sort results by average score (descending)
                sorted_results = sorted(self.results_data, key=lambda x: x.get('average_score', 0), reverse=True)
                print(f"DEBUG: Sorted {len(sorted_results)} results")

                with open(save_path, 'w', newline='', encoding='utf-8') as csvfile:
                    # Create fieldnames dynamically
                    fieldnames = ['Rank', 'File Name', 'Average Score', 'Success']

                    # Add parameter columns if available
                    if self.parameters:
                        fieldnames.extend(self.parameters)

                    fieldnames.append('Overall Comments')
                    print(f"DEBUG: CSV fieldnames: {fieldnames}")

                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()

                    for i, result in enumerate(sorted_results):
                        # Basic row data
                        row = {
                            'Rank': i + 1,
                            'File Name': result.get('file_name', 'Unknown'),
                            'Average Score': f"{result.get('average_score', 0):.2f}",
                            'Success': 'Yes' if result.get('success', False) else 'No',
                            'Overall Comments': result.get('overall_comments', 'No comments')
                        }

                        # Add individual parameter scores
                        scores = result.get('scores', {})
                        for param in self.parameters:
                            score = scores.get(param, 0)
                            row[param] = f"{score:.2f}"

                        writer.writerow(row)
                        print(f"DEBUG: Wrote row {i+1}")

                print("DEBUG: CSV file written successfully")
                messagebox.showinfo("Export Successful", f"Results exported successfully to:\n{save_path}")

            except PermissionError:
                print("DEBUG: Permission error writing CSV")
                messagebox.showerror("Permission Error", f"Cannot write to file. Please check if the file is open in another program:\n{save_path}")
            except Exception as e:
                print(f"DEBUG: Error writing CSV: {e}")
                messagebox.showerror("Export Error", f"Failed to export results:\n{str(e)}")
        except Exception as e:
            print(f"DEBUG: Error in export_results: {e}")
            messagebox.showerror("Debug Error", f"Export error: {str(e)}")
    
    def clear_results(self):
        """
        Clear all results from the display
        """
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        self.results_data = []
        self.parameters = []
        self.results_info_label.config(text="No evaluation results yet")
        self.export_button.config(state="disabled")

