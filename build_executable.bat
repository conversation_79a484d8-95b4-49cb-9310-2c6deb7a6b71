@echo off
echo Building Talent Hero Evaluator Executable...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again
    pause
    exit /b 1
)

REM Install PyInstaller if needed
echo Installing PyInstaller...
pip install pyinstaller==6.14.1

REM Run the build script
echo.
echo Running build script...
python build_executable.py

echo.
echo Build process completed!
pause
