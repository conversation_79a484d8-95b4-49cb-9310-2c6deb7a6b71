# Building Executable for Talent Hero Evaluator

## Quick Build Process

### Option 1: Using the Build Script (Recommended)

1. **Install PyInstaller** (if not already installed):
   ```bash
   pip install pyinstaller==6.3.0
   ```

2. **Run the build script**:
   ```bash
   python build_executable.py
   ```

3. **Or use the batch file** (Windows):
   ```bash
   build_executable.bat
   ```

### Option 2: Manual PyInstaller Command

```bash
pyinstaller --onefile --windowed --name "TalentHeroEvaluator" --add-data "config;config" --add-data "gui;gui" --add-data "core;core" --add-data "utils;utils" main.py
```

## What Gets Created

After building, you'll find:
- `dist/TalentHeroEvaluator.exe` - The main executable
- `dist/README_EXECUTABLE.txt` - Instructions for end users
- `build/` folder - Temporary build files (can be deleted)
- `talent_hero_evaluator.spec` - PyInstaller specification file

## Distribution

### For End Users:
1. **Copy the entire `dist` folder** to the target machine
2. **Ensure Ollama is installed** on the target machine:
   - Download from https://ollama.ai
   - Install and run `ollama serve`
   - Download at least one model: `ollama pull llama2`
3. **Run `TalentHeroEvaluator.exe`**

### File Size
- Expected size: ~50-100MB (includes Python runtime and all dependencies)
- No Python installation required on target machine
- No pip packages need to be installed

## Requirements for Target Machine

### ✅ What's Included:
- Python runtime
- All Python packages (PyPDF2, requests, Pillow, etc.)
- Tkinter GUI framework
- All application code

### ❌ What's NOT Included (must be installed separately):
- **Ollama** - Must be installed and running
- **Ollama models** - Must be downloaded (e.g., `ollama pull llama2`)

## Troubleshooting Build Issues

### "PyInstaller not found"
```bash
pip install pyinstaller==6.3.0
```

### "Module not found" errors
Make sure all dependencies are installed:
```bash
pip install -r requirements.txt
```

### Large executable size
This is normal - the executable includes the entire Python runtime and all dependencies.

### Build fails on import errors
Add missing modules to the `hiddenimports` list in the spec file.

## Testing the Executable

1. **Test locally first**:
   - Run the executable on your development machine
   - Ensure all features work correctly

2. **Test on clean machine**:
   - Copy `dist` folder to a machine without Python
   - Install only Ollama
   - Test the application

## Advanced Options

### Custom Icon
Add an icon file and update the spec file:
```python
icon='path/to/icon.ico'
```

### Console Window
To show console for debugging, change in spec file:
```python
console=True
```

### Smaller Executable
Use `--onedir` instead of `--onefile` for faster startup but larger distribution folder.

## Security Notes

- Windows Defender may flag the executable as suspicious (common with PyInstaller)
- Consider code signing for production distribution
- Test on target machines before wide distribution

## Build Environment

- **Recommended**: Build on the same OS as target deployment
- **Python Version**: Use Python 3.8+ for best compatibility
- **Dependencies**: Ensure all packages are up to date
