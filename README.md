# Talent Hero Evaluator

A powerful Tkinter application for evaluating resumes against job descriptions using AI. The application uses Ollama with the cogito:3b model to provide intelligent scoring and ranking of resumes based on customizable evaluation parameters.

## Features

- 📁 **File Upload**: Upload a single job description PDF and multiple resume PDFs
- 🤖 **Model Selection**: Choose from available Ollama models for evaluation
- ⚙️ **Customizable Parameters**: Select 5-10 evaluation criteria from predefined options or add custom parameters
- 🤖 **AI-Powered Evaluation**: Uses your selected Ollama model for intelligent resume scoring
- 📊 **Ranked Results**: View resumes ranked by average score with detailed breakdowns
- 💾 **Download & Export**: Click to download individual resumes or export all results to CSV
- 🎯 **Real-time Progress**: Track evaluation progress with visual feedback

## Prerequisites

### 1. Python Requirements
- Python 3.7 or higher
- Required packages (see `requirements.txt`)

### 2. Ollama Setup
1. Install Ollama from [https://ollama.ai](https://ollama.ai)
2. Start Ollama service
3. Pull the cogito:3b model:
   ```bash
   ollama pull cogito:3b
   ```
4. Ensure Ollama is running on `localhost:11434`

## Installation

1. **Clone or download this repository**
   ```bash
   git clone <repository-url>
   cd talent_hero_bulk_resume_evaluator
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify Ollama is running**
   ```bash
   ollama list
   ```
   Make sure `cogito:3b` is in the list of available models.

## Usage

### 1. Start the Application
```bash
python main.py
```

### 2. Upload Files
- **Job Description**: Click "Select Job Description PDF" to upload a single PDF containing the job requirements
- **Resumes**: Click "Select Resume PDFs" to upload multiple resume files (supports batch selection)

### 3. Select AI Model
- Switch to the "Model" tab
- Choose from available Ollama models (the app will automatically detect installed models)
- Test the connection to ensure the selected model is working

### 4. Configure Evaluation Parameters
- Switch to the "Parameters" tab
- Select 5-10 evaluation criteria from the predefined list:
  - Skills Match
  - Experience Relevance
  - Education Fit
  - Technical Competency
  - Communication Skills
  - Leadership Experience
  - Industry Knowledge
  - Cultural Fit
  - Career Progression
  - Certifications & Training
- Add custom parameters if needed

### 5. Run Evaluation
- Click "🚀 Start Evaluation" to begin the AI-powered analysis
- Monitor progress in the progress bar
- The application will process each resume against the job description

### 6. View Results
- Switch to the "Results" tab to see ranked resumes
- Results include:
  - Overall ranking (1 to n)
  - Average score (0-10)
  - Individual parameter scores
  - AI-generated comments
- **Download resumes**: Double-click any resume to download it
- **Export results**: Click "Export to CSV" to save all results
- **Save results**: Click "Save Results" to create a JSON file for future incremental evaluation

### 7. Incremental Evaluation (Optional)
- For future evaluations with the same job description:
  1. Check "Enable incremental evaluation"
  2. Browse and select your saved results file
  3. Add new resume files to the existing batch
  4. Run evaluation - only new resumes will be processed

## Application Structure

```
talent_hero_bulk_resume_evaluator/
├── main.py                     # Application entry point
├── requirements.txt            # Python dependencies
├── README.md                  # This file
├── config/
│   └── settings.py            # Application configuration
├── core/
│   ├── evaluator.py           # Main evaluation engine
│   ├── ollama_client.py       # Ollama API integration
│   └── pdf_processor.py       # PDF text extraction
├── gui/
│   ├── main_window.py         # Main application window
│   ├── file_upload_frame.py   # File upload interface
│   ├── parameters_frame.py    # Parameter selection
│   └── results_frame.py       # Results display and ranking
└── utils/
    └── file_utils.py          # File handling utilities
```

## Configuration

Edit `config/settings.py` to customize:
- Ollama server URL and model
- Default evaluation parameters
- File size limits
- UI dimensions

## Troubleshooting

### Installation Issues

1. **PyMuPDF Installation Error on Windows**
   If you encounter build errors when installing PyMuPDF (like "No match found for: C:\Program Files*\Microsoft Visual Studio\2*\*"):

   **Solution**: The requirements.txt has been updated to use PyMuPDF>=1.24.0 which includes pre-built wheels for Windows. Simply run:
   ```bash
   pip install -r requirements.txt
   ```

   If you still have issues, try:
   ```bash
   pip install --upgrade pip
   pip install PyMuPDF --upgrade
   ```

2. **"Missing dependencies"**
   - Install required packages: `pip install -r requirements.txt`
   - Ensure Python version is 3.7+

### Runtime Issues

1. **"Cannot connect to Ollama"**
   - Ensure Ollama is running: `ollama serve`
   - Check if cogito:3b model is available: `ollama list`
   - Verify Ollama is on port 11434

2. **"PDF processing failed"**
   - Ensure PDF files are not encrypted
   - Check file size (default limit: 10MB)
   - Verify files are valid PDF format

3. **Slow evaluation**
   - Large PDF files take longer to process
   - Complex job descriptions may require more processing time
   - Consider reducing the number of evaluation parameters

4. **Incremental evaluation not working**
   - Ensure the job description file/content hasn't changed
   - Verify evaluation parameters are identical
   - Check that the results file is valid JSON format
   - If parameters or job description changed, perform a full re-evaluation

### Testing Ollama Connection

Use the built-in connection test:
1. Go to "Tools" → "Test Ollama Connection"
2. This will verify Ollama is accessible and the model is available

## Performance Tips

- **File Size**: Keep PDF files under 5MB for optimal performance
- **Batch Size**: Process 10-20 resumes at a time for best results
- **Parameters**: Use 5-7 parameters for faster evaluation
- **Text Quality**: Ensure PDFs have good text extraction (avoid scanned images)

## Incremental Evaluation

The application now supports incremental evaluation, allowing you to add more resumes to an existing evaluation without re-processing previously evaluated resumes.

### How It Works

1. **Enable Incremental Mode**: Check the "Enable incremental evaluation" checkbox
2. **Load Previous Results**: Browse and select a previously saved results file (JSON format)
3. **Add New Resumes**: Upload additional resume files to the existing batch
4. **Run Evaluation**: Only new resumes will be processed; existing results are preserved

### Benefits

- **Time Saving**: Avoid re-evaluating resumes you've already processed
- **Cost Effective**: Reduce API calls to AI services
- **Scalable**: Easily expand your candidate pool over time
- **Consistent**: Maintains the same job description and evaluation parameters

### Requirements for Incremental Evaluation

- Same job description file (or content)
- Same evaluation parameters
- Valid results file from previous evaluation

If the job description or parameters change, the system will automatically perform a full re-evaluation.

### Saving and Loading Results

- **Save Results**: After any evaluation, click "Save Results" to create a JSON file
- **Load Results**: Use "Browse" to select an existing results file for incremental evaluation
- **File Format**: Results are saved in JSON format with metadata and evaluation data

## Export Options

Results can be exported to CSV format including:
- Resume rankings
- Individual parameter scores
- Average scores
- AI-generated comments
- File names and paths

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify all prerequisites are met
3. Test Ollama connection independently
4. Check application logs for detailed error messages

## License

This project is provided as-is for educational and professional use.
