"""
Enhanced document processing for PDFs and Word documents
"""
import os
import io
from pathlib import Path
import PyPDF2
import fitz  # PyMuPDF for better PDF handling
from docx import Document
import mammoth  # For .doc files
import re

class DocumentProcessor:
    """
    Enhanced processor for PDF and Word documents with better text extraction
    """
    
    def __init__(self):
        self.supported_extensions = ['.pdf', '.docx', '.doc']
    
    def extract_text_from_document(self, file_path):
        """
        Extract text from PDF or Word document with enhanced handling
        
        Args:
            file_path (str): Path to the document
            
        Returns:
            tuple: (success: bool, content: str or error_message: str)
        """
        try:
            file_ext = Path(file_path).suffix.lower()
            
            if file_ext == '.pdf':
                return self._extract_from_pdf(file_path)
            elif file_ext == '.docx':
                return self._extract_from_docx(file_path)
            elif file_ext == '.doc':
                return self._extract_from_doc(file_path)
            else:
                return False, f"Unsupported file format: {file_ext}"
                
        except Exception as e:
            return False, f"Error processing document: {str(e)}"
    
    def _extract_from_pdf(self, file_path):
        """Extract text from PDF using multiple methods for better accuracy"""
        text_content = ""
        
        try:
            # Method 1: Try PyMuPDF first (better for complex layouts)
            doc = fitz.open(file_path)
            for page_num in range(doc.page_count):
                page = doc[page_num]
                text_content += page.get_text()
                text_content += "\n"
            doc.close()
            
            # If PyMuPDF didn't extract much text, try PyPDF2
            if len(text_content.strip()) < 100:
                text_content = self._extract_with_pypdf2(file_path)
                
        except Exception as e:
            # Fallback to PyPDF2
            try:
                text_content = self._extract_with_pypdf2(file_path)
            except Exception as e2:
                return False, f"Failed to extract text from PDF: {str(e2)}"
        
        # Clean and validate extracted text
        cleaned_text = self._clean_extracted_text(text_content)
        
        if len(cleaned_text.strip()) < 50:
            return False, "Document appears to be mostly images or has unreadable text"
        
        return True, cleaned_text
    
    def _extract_with_pypdf2(self, file_path):
        """Fallback PDF extraction using PyPDF2"""
        text_content = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text_content += page.extract_text()
                text_content += "\n"
        return text_content
    
    def _extract_from_docx(self, file_path):
        """Extract text from DOCX file including tables"""
        try:
            doc = Document(file_path)
            text_content = []
            
            # Extract paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            # Extract tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content.append(" | ".join(row_text))
            
            full_text = "\n".join(text_content)
            cleaned_text = self._clean_extracted_text(full_text)
            
            if len(cleaned_text.strip()) < 50:
                return False, "Document appears to be empty or has insufficient text"
            
            return True, cleaned_text
            
        except Exception as e:
            return False, f"Error reading DOCX file: {str(e)}"
    
    def _extract_from_doc(self, file_path):
        """Extract text from DOC file using mammoth"""
        try:
            with open(file_path, "rb") as docx_file:
                result = mammoth.extract_raw_text(docx_file)
                text_content = result.value
            
            cleaned_text = self._clean_extracted_text(text_content)
            
            if len(cleaned_text.strip()) < 50:
                return False, "Document appears to be empty or has insufficient text"
            
            return True, cleaned_text
            
        except Exception as e:
            return False, f"Error reading DOC file: {str(e)}"
    
    def _clean_extracted_text(self, text):
        """Clean and normalize extracted text"""
        if not text:
            return ""
        
        # Remove excessive whitespace and normalize line breaks
        text = re.sub(r'\n\s*\n', '\n\n', text)  # Multiple newlines to double
        text = re.sub(r'[ \t]+', ' ', text)      # Multiple spaces/tabs to single space
        text = re.sub(r'\n ', '\n', text)        # Remove spaces after newlines
        
        # Remove common PDF artifacts
        text = re.sub(r'[^\w\s\n\-.,;:()@#$%&*+=<>?/\\|"\'`~![\]{}]', ' ', text)
        
        return text.strip()