"""
Resume evaluation engine
"""
import os
import json
import hashlib
from pathlib import Path
from datetime import datetime
from core.document_processor import DocumentProcessor
from core.ollama_client import OllamaClient
from core.groq_client import GroqClient


class ResumeEvaluator:
    """
    Main class for evaluating resumes against job descriptions
    """
    
    def __init__(self, model=None):
        self.document_processor = DocumentProcessor()
        self.ollama_client = OllamaClient(model)
        self.groq_client = GroqClient()
        self.current_provider = "groq"  # Default to Groq
        self.job_description_text = None
        self.job_description_hash = None
        self.evaluation_results = []
        self.results_file_path = None
    
    def load_job_description(self, job_description_path):
        """
        Load and process job description document
        
        Args:
            job_description_path (str): Path to job description document
            
        Returns:
            tuple: (success: bool, message: str)
        """
        success, content = self.document_processor.extract_text_from_document(job_description_path)
        
        if success:
            self.job_description_text = content
            # Create hash of job description for incremental evaluation
            self.job_description_hash = hashlib.md5(content.encode('utf-8')).hexdigest()
            return True, "Job description loaded successfully"
        else:
            return False, f"Failed to load job description: {content}"
    
    def evaluate_resumes(self, resume_paths, parameters, progress_callback=None, result_callback=None,
                        incremental=False, existing_results_path=None):
        """
        Evaluate multiple resumes with real-time updates and incremental support

        Args:
            resume_paths (list): List of resume file paths
            parameters (list): Evaluation parameters
            progress_callback (function): Optional callback for progress updates
            result_callback (function): Optional callback for real-time results
            incremental (bool): Whether to perform incremental evaluation
            existing_results_path (str): Path to existing results file for incremental evaluation

        Returns:
            tuple: (success: bool, results: list or error_message: str)
        """
        if not self.job_description_text:
            return False, "Job description not loaded"
        
        if not resume_paths:
            return False, "No resume files provided"
        
        # Test Ollama connection first
        # Test connection for current provider
        if self.current_provider == "ollama":
            connection_success, connection_message = self.ollama_client.test_connection()
        else:  # groq
            connection_success, connection_message = self.groq_client.test_connection()

        if not connection_success:
            return False, connection_message

        # Handle incremental evaluation
        existing_results = {}
        processed_files = set()

        if incremental and existing_results_path:
            success, data = self.load_results_from_file(existing_results_path)
            if success:
                # Check if job description matches
                if data['metadata'].get('job_description_hash') == self.job_description_hash:
                    # Check if parameters match
                    if set(data['metadata'].get('parameters', [])) == set(parameters):
                        # Load existing results
                        for result in data['results']:
                            file_hash = self.get_file_hash(result['file_path'])
                            existing_results[file_hash] = result
                            processed_files.add(result['file_path'])
                        self.evaluation_results = data['results'].copy()
                    else:
                        # Parameters changed, start fresh but inform user
                        if progress_callback:
                            progress_callback(0, len(resume_paths), "Parameters changed - evaluating all resumes...")
                else:
                    # Job description changed, start fresh
                    if progress_callback:
                        progress_callback(0, len(resume_paths), "Job description changed - evaluating all resumes...")

        # Filter out already processed files
        if incremental:
            new_resume_paths = [path for path in resume_paths if path not in processed_files]
            if not new_resume_paths:
                # All files already processed
                if progress_callback:
                    progress_callback(len(resume_paths), len(resume_paths), "All resumes already evaluated")
                return True, self.evaluation_results
        else:
            # Clear previous results for fresh evaluation
            self.evaluation_results = []
            new_resume_paths = resume_paths

        total_resumes = len(resume_paths)
        new_resumes_count = len(new_resume_paths)
        already_processed = total_resumes - new_resumes_count

        # Update progress
        if progress_callback:
            if incremental and already_processed > 0:
                progress_callback(already_processed, total_resumes,
                               f"Found {already_processed} already processed resumes, evaluating {new_resumes_count} new ones...")
            else:
                progress_callback(0, total_resumes, "Starting evaluation...")

        for i, resume_path in enumerate(new_resume_paths):
            try:
                # Update progress
                if progress_callback:
                    current_progress = already_processed + i
                    progress_callback(current_progress, total_resumes, f"Processing {os.path.basename(resume_path)}")
                
                # Extract text from resume
                success, resume_text = self.document_processor.extract_text_from_document(resume_path)
                
                if not success:
                    result = {
                        'file_path': resume_path,
                        'file_name': os.path.basename(resume_path),
                        'success': False,
                        'error': resume_text,
                        'average_score': 0,
                        'scores': {},
                        'overall_comments': 'Failed to process document',
                        'status': 'failed'
                    }
                    self.evaluation_results.append(result)
                    if result_callback:
                        current_count = already_processed + i + 1
                        result_callback(result, current_count, total_resumes)
                    continue
                
                # Evaluate resume
                if self.current_provider == "ollama":
                    eval_success, evaluation = self.ollama_client.evaluate_resume(
                        self.job_description_text, resume_text, parameters
                    )
                else:
                    eval_success, evaluation = self.groq_client.evaluate_resume(
                        self.job_description_text, resume_text, parameters
                    )
                
                if eval_success:
                    result = {
                        'file_path': resume_path,
                        'file_name': os.path.basename(resume_path),
                        'success': True,
                        'average_score': evaluation['average_score'],
                        'scores': evaluation['scores'],
                        'overall_comments': evaluation['overall_comments'],
                        'status': 'completed'
                    }
                else:
                    result = {
                        'file_path': resume_path,
                        'file_name': os.path.basename(resume_path),
                        'success': False,
                        'error': evaluation,
                        'average_score': 0,
                        'scores': {},
                        'overall_comments': 'Evaluation failed',
                        'status': 'failed'
                    }
                
                self.evaluation_results.append(result)
                
                # Real-time result callback
                if result_callback:
                    current_count = already_processed + i + 1
                    result_callback(result, current_count, total_resumes)
                
            except Exception as e:
                result = {
                    'file_path': resume_path,
                    'file_name': os.path.basename(resume_path),
                    'success': False,
                    'error': str(e),
                    'average_score': 0,
                    'scores': {},
                    'overall_comments': 'Unexpected error during evaluation',
                    'status': 'failed'
                }
                self.evaluation_results.append(result)
                if result_callback:
                    current_count = already_processed + i + 1
                    result_callback(result, current_count, total_resumes)
        
        return True, self.evaluation_results

    def set_provider(self, provider):
        """Set the current AI provider (ollama or groq)"""
        self.current_provider = provider

    def get_groq_models(self):
        """Get available Groq models"""
        return self.groq_client.get_available_models()

    def get_current_provider(self):
        """Get the current provider"""
        return getattr(self, 'current_provider', 'groq')
    def get_ranked_results(self):
        """
        Get evaluation results ranked by average score
        
        Returns:
            list: Ranked results with rank numbers
        """
        if not self.evaluation_results:
            return []
        
        # Sort by average score (descending)
        sorted_results = sorted(
            self.evaluation_results, 
            key=lambda x: x['average_score'], 
            reverse=True
        )
        
        # Add rank numbers
        for i, result in enumerate(sorted_results):
            result['rank'] = i + 1
        
        return sorted_results

    def set_model(self, model_name):
        """
        Set the Ollama model to use for evaluation

        Args:
            model_name (str): Name of the model to use
        """
        self.ollama_client.set_model(model_name)

    def get_available_models(self):
        """
        Get list of available models for current provider

        Returns:
            tuple: (success: bool, models: list or error_message: str)
        """
        if self.current_provider == "ollama":
            return self.ollama_client.get_available_models()
        else:  # groq
            return self.groq_client.get_available_models()

    def export_results_to_csv(self, output_path, parameters):
        """
        Export evaluation results to CSV file
        
        Args:
            output_path (str): Path for output CSV file
            parameters (list): Evaluation parameters
            
        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            import csv
            
            ranked_results = self.get_ranked_results()
            
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['Rank', 'File Name', 'Average Score', 'Overall Comments'] + parameters
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                
                for result in ranked_results:
                    row = {
                        'Rank': result['rank'],
                        'File Name': result['file_name'],
                        'Average Score': f"{result['average_score']:.2f}",
                        'Overall Comments': result['overall_comments']
                    }
                    
                    # Add individual parameter scores
                    for param in parameters:
                        score = result['scores'].get(param, 0)
                        row[param] = f"{score:.2f}"
                    
                    writer.writerow(row)
            
            return True, f"Results exported to {output_path}"

        except Exception as e:
            return False, f"Failed to export results: {str(e)}"

    def save_results_to_file(self, output_path, parameters):
        """
        Save evaluation results to JSON file for incremental evaluation

        Args:
            output_path (str): Path for output JSON file
            parameters (list): Evaluation parameters

        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            results_data = {
                'metadata': {
                    'timestamp': datetime.now().isoformat(),
                    'job_description_hash': self.job_description_hash,
                    'parameters': parameters,
                    'provider': self.current_provider
                },
                'results': self.evaluation_results
            }

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, indent=2, ensure_ascii=False)

            self.results_file_path = output_path
            return True, f"Results saved to {output_path}"

        except Exception as e:
            return False, f"Error saving results: {str(e)}"

    def load_results_from_file(self, input_path):
        """
        Load evaluation results from JSON file for incremental evaluation

        Args:
            input_path (str): Path to input JSON file

        Returns:
            tuple: (success: bool, data: dict or error_message: str)
        """
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Validate structure
            if 'metadata' not in data or 'results' not in data:
                return False, "Invalid results file format"

            self.results_file_path = input_path
            return True, data

        except FileNotFoundError:
            return False, "Results file not found"
        except json.JSONDecodeError:
            return False, "Invalid JSON format in results file"
        except Exception as e:
            return False, f"Error loading results: {str(e)}"

    def get_file_hash(self, file_path):
        """
        Generate hash for a file to check if it's already been processed

        Args:
            file_path (str): Path to the file

        Returns:
            str: MD5 hash of the file
        """
        try:
            with open(file_path, 'rb') as f:
                file_hash = hashlib.md5()
                for chunk in iter(lambda: f.read(4096), b""):
                    file_hash.update(chunk)
                return file_hash.hexdigest()
        except Exception:
            # If we can't hash the file, use the file path as fallback
            return hashlib.md5(file_path.encode('utf-8')).hexdigest()
