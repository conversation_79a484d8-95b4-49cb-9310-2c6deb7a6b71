"""
Resume evaluation engine
"""
import os
from pathlib import Path
from core.document_processor import DocumentProcessor
from core.ollama_client import OllamaClient
from core.groq_client import GroqClient


class ResumeEvaluator:
    """
    Main class for evaluating resumes against job descriptions
    """
    
    def __init__(self, model=None):
        self.document_processor = DocumentProcessor()
        self.ollama_client = OllamaClient(model)
        self.groq_client = GroqClient()
        self.current_provider = "groq"  # Default to Groq
        self.job_description_text = None
        self.evaluation_results = []
    
    def load_job_description(self, job_description_path):
        """
        Load and process job description document
        
        Args:
            job_description_path (str): Path to job description document
            
        Returns:
            tuple: (success: bool, message: str)
        """
        success, content = self.document_processor.extract_text_from_document(job_description_path)
        
        if success:
            self.job_description_text = content
            return True, "Job description loaded successfully"
        else:
            return False, f"Failed to load job description: {content}"
    
    def evaluate_resumes(self, resume_paths, parameters, progress_callback=None, result_callback=None):
        """
        Evaluate multiple resumes with real-time updates
        
        Args:
            resume_paths (list): List of resume file paths
            parameters (list): Evaluation parameters
            progress_callback (function): Optional callback for progress updates
            result_callback (function): Optional callback for real-time results
            
        Returns:
            tuple: (success: bool, results: list or error_message: str)
        """
        if not self.job_description_text:
            return False, "Job description not loaded"
        
        if not resume_paths:
            return False, "No resume files provided"
        
        # Test Ollama connection first
        # Test connection for current provider
        if self.current_provider == "ollama":
            connection_success, connection_message = self.ollama_client.test_connection()
        else:  # groq
            connection_success, connection_message = self.groq_client.test_connection()

        if not connection_success:
            return False, connection_message
        
        self.evaluation_results = []
        total_resumes = len(resume_paths)
        
        for i, resume_path in enumerate(resume_paths):
            try:
                # Update progress
                if progress_callback:
                    progress_callback(i, total_resumes, f"Processing {os.path.basename(resume_path)}")
                
                # Extract text from resume
                success, resume_text = self.document_processor.extract_text_from_document(resume_path)
                
                if not success:
                    result = {
                        'file_path': resume_path,
                        'file_name': os.path.basename(resume_path),
                        'success': False,
                        'error': resume_text,
                        'average_score': 0,
                        'scores': {},
                        'overall_comments': 'Failed to process document',
                        'status': 'failed'
                    }
                    self.evaluation_results.append(result)
                    if result_callback:
                        result_callback(result, i + 1, total_resumes)
                    continue
                
                # Evaluate resume
                if self.current_provider == "ollama":
                    eval_success, evaluation = self.ollama_client.evaluate_resume(
                        self.job_description_text, resume_text, parameters
                    )
                else:
                    eval_success, evaluation = self.groq_client.evaluate_resume(
                        self.job_description_text, resume_text, parameters
                    )
                
                if eval_success:
                    result = {
                        'file_path': resume_path,
                        'file_name': os.path.basename(resume_path),
                        'success': True,
                        'average_score': evaluation['average_score'],
                        'scores': evaluation['scores'],
                        'overall_comments': evaluation['overall_comments'],
                        'status': 'completed'
                    }
                else:
                    result = {
                        'file_path': resume_path,
                        'file_name': os.path.basename(resume_path),
                        'success': False,
                        'error': evaluation,
                        'average_score': 0,
                        'scores': {},
                        'overall_comments': 'Evaluation failed',
                        'status': 'failed'
                    }
                
                self.evaluation_results.append(result)
                
                # Real-time result callback
                if result_callback:
                    result_callback(result, i + 1, total_resumes)
                
            except Exception as e:
                result = {
                    'file_path': resume_path,
                    'file_name': os.path.basename(resume_path),
                    'success': False,
                    'error': str(e),
                    'average_score': 0,
                    'scores': {},
                    'overall_comments': 'Unexpected error during evaluation',
                    'status': 'failed'
                }
                self.evaluation_results.append(result)
                if result_callback:
                    result_callback(result, i + 1, total_resumes)
        
        return True, self.evaluation_results

    def set_provider(self, provider):
        """Set the current AI provider (ollama or groq)"""
        self.current_provider = provider

    def get_groq_models(self):
        """Get available Groq models"""
        return self.groq_client.get_available_models()

    def get_current_provider(self):
        """Get the current provider"""
        return getattr(self, 'current_provider', 'groq')
    def get_ranked_results(self):
        """
        Get evaluation results ranked by average score
        
        Returns:
            list: Ranked results with rank numbers
        """
        if not self.evaluation_results:
            return []
        
        # Sort by average score (descending)
        sorted_results = sorted(
            self.evaluation_results, 
            key=lambda x: x['average_score'], 
            reverse=True
        )
        
        # Add rank numbers
        for i, result in enumerate(sorted_results):
            result['rank'] = i + 1
        
        return sorted_results

    def set_model(self, model_name):
        """
        Set the Ollama model to use for evaluation

        Args:
            model_name (str): Name of the model to use
        """
        self.ollama_client.set_model(model_name)

    def get_available_models(self):
        """
        Get list of available models for current provider

        Returns:
            tuple: (success: bool, models: list or error_message: str)
        """
        if self.current_provider == "ollama":
            return self.ollama_client.get_available_models()
        else:  # groq
            return self.groq_client.get_available_models()

    def export_results_to_csv(self, output_path, parameters):
        """
        Export evaluation results to CSV file
        
        Args:
            output_path (str): Path for output CSV file
            parameters (list): Evaluation parameters
            
        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            import csv
            
            ranked_results = self.get_ranked_results()
            
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['Rank', 'File Name', 'Average Score', 'Overall Comments'] + parameters
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                
                for result in ranked_results:
                    row = {
                        'Rank': result['rank'],
                        'File Name': result['file_name'],
                        'Average Score': f"{result['average_score']:.2f}",
                        'Overall Comments': result['overall_comments']
                    }
                    
                    # Add individual parameter scores
                    for param in parameters:
                        score = result['scores'].get(param, 0)
                        row[param] = f"{score:.2f}"
                    
                    writer.writerow(row)
            
            return True, f"Results exported to {output_path}"
            
        except Exception as e:
            return False, f"Failed to export results: {str(e)}"
