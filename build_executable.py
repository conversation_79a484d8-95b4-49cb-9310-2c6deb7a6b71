"""
Build script to create executable for Talent Hero Evaluator
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_pyinstaller():
    """Check if PyInstaller is installed"""
    try:
        import PyInstaller
        print("✓ PyInstaller is available")
        return True
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller==6.14.1"])
            print("✓ PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install PyInstaller")
            return False


def create_spec_file():
    """Create PyInstaller spec file for better control"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('gui', 'gui'),
        ('core', 'core'),
        ('utils', 'utils'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'PyPDF2',
        'PyPDF2._reader',
        'PyPDF2._writer',
        'PyPDF2.errors',
        'requests',
        'requests.adapters',
        'requests.auth',
        'requests.exceptions',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'csv',
        'json',
        'threading',
        'pathlib',
        'shutil',
        'os',
        'sys',
        'io',
        'tempfile',
        'datetime',
        'typing'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='TalentHeroEvaluator',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # Set to True if you want console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # Add icon path here if you have one
)
'''
    
    with open('talent_hero_evaluator.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content.strip())
    
    print("✓ Created PyInstaller spec file")


def build_executable():
    """Build the executable using PyInstaller"""
    print("🚀 Building executable...")
    
    try:
        # Build using the spec file
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "talent_hero_evaluator.spec"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Executable built successfully!")
            
            # Check if executable exists
            exe_path = Path("dist/TalentHeroEvaluator.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"✓ Executable created: {exe_path}")
                print(f"✓ File size: {size_mb:.1f} MB")
                return True
            else:
                print("❌ Executable file not found in dist folder")
                return False
        else:
            print("❌ Build failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error during build: {e}")
        return False


def create_readme_for_exe():
    """Create a README file for the executable distribution"""
    readme_content = """
# Talent Hero Evaluator - Executable Version

## Quick Start

1. **Download and Extract**: Extract all files to a folder
2. **Run the Application**: Double-click `TalentHeroEvaluator.exe`
3. **Ensure Ollama is Running**: Make sure Ollama is installed and running on localhost:11434

## Prerequisites

### Ollama Setup (Required)
The application requires Ollama to be installed and running:

1. **Download Ollama**: Visit https://ollama.ai and download for your OS
2. **Install Ollama**: Follow the installation instructions
3. **Start Ollama**: Run `ollama serve` in command prompt/terminal
4. **Install Models**: Download at least one model, for example:
   ```
   ollama pull llama2
   ollama pull mistral
   ollama pull cogito:3b
   ```

## Usage

1. **Start Ollama**: Ensure Ollama is running (`ollama serve`)
2. **Run Application**: Double-click `TalentHeroEvaluator.exe`
3. **Upload Files**: 
   - Upload one job description PDF
   - Upload multiple resume PDFs
4. **Select Model**: Choose from available Ollama models
5. **Configure Parameters**: Select 5-10 evaluation criteria
6. **Run Evaluation**: Click "Start Evaluation"
7. **View Results**: See ranked resumes and download/export results

## Troubleshooting

### "Cannot connect to Ollama"
- Ensure Ollama is running: `ollama serve`
- Check if models are available: `ollama list`
- Verify Ollama is on port 11434

### Application Won't Start
- Check Windows Defender/Antivirus (may block unsigned executables)
- Run as Administrator if needed
- Ensure all files are extracted together

### Slow Performance
- Large PDF files take longer to process
- Complex models may be slower
- Consider using smaller/faster models for quick testing

## File Structure
```
TalentHeroEvaluator/
├── TalentHeroEvaluator.exe    # Main application
├── README_EXECUTABLE.txt      # This file
└── [other bundled files]      # Required dependencies
```

## Support

For issues:
1. Ensure Ollama is properly installed and running
2. Check that you have at least one model downloaded
3. Verify PDF files are not corrupted or encrypted
4. Try running with different Ollama models

## System Requirements

- Windows 10/11 (64-bit)
- 4GB RAM minimum (8GB recommended)
- 500MB free disk space
- Ollama installed and running
- Internet connection (for Ollama model downloads)
"""
    
    with open('dist/README_EXECUTABLE.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content.strip())
    
    print("✓ Created README for executable distribution")


def main():
    """Main build process"""
    print("🔨 Building Talent Hero Evaluator Executable")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("❌ main.py not found. Please run this script from the project root directory.")
        return False
    
    # Check PyInstaller
    if not check_pyinstaller():
        return False
    
    # Create spec file
    create_spec_file()
    
    # Build executable
    if not build_executable():
        return False
    
    # Create README for distribution
    create_readme_for_exe()
    
    print("\n" + "=" * 50)
    print("🎉 BUILD COMPLETE!")
    print("📁 Executable location: dist/TalentHeroEvaluator.exe")
    print("📋 Distribution README: dist/README_EXECUTABLE.txt")
    print("\n📦 To distribute:")
    print("   1. Copy the entire 'dist' folder")
    print("   2. Ensure Ollama is installed on target machine")
    print("   3. Run TalentHeroEvaluator.exe")
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        input("\nPress Enter to exit...")
        sys.exit(1)
    else:
        input("\nPress Enter to exit...")
