"""
Model selection frame for choosing AI models (Ollama or Groq)
"""
import tkinter as tk
from tkinter import ttk, messagebox
from config.settings import OLLAMA_MODEL


class ModelSelectionFrame(ttk.Frame):
    """
    Frame for selecting AI model provider and model
    """

    def __init__(self, parent, evaluator):
        super().__init__(parent)
        self.evaluator = evaluator
        self.available_models = []
        self.selected_model = OLLAMA_MODEL
        self.selected_provider = "groq"  # Default to Groq
        self.create_widgets()
        self.refresh_models()
    
    def create_widgets(self):
        """
        Create and layout widgets
        """
        # Main frame
        main_frame = ttk.LabelFrame(self, text="Model Selection", padding="10")
        main_frame.pack(fill="x", padx=10, pady=5)

        # Provider selection frame
        provider_frame = ttk.LabelFrame(main_frame, text="AI Provider", padding="5")
        provider_frame.pack(fill="x", pady=(0, 10))

        self.provider_var = tk.StringVar(value="groq")

        # Groq radio button (default)
        self.groq_radio = ttk.Radiobutton(
            provider_frame,
            text="Groq (Cloud) - Default",
            variable=self.provider_var,
            value="groq",
            command=self.on_provider_changed
        )
        self.groq_radio.pack(side="left", padx=(0, 20))

        # Ollama radio button
        self.ollama_radio = ttk.Radiobutton(
            provider_frame,
            text="Ollama (Local)",
            variable=self.provider_var,
            value="ollama",
            command=self.on_provider_changed
        )
        self.ollama_radio.pack(side="left")

        # Instructions
        instructions = ttk.Label(
            main_frame,
            text="Select the Ollama model to use for resume evaluation:",
            font=("TkDefaultFont", 9)
        )
        instructions.pack(anchor="w", pady=(0, 10))
        
        # Model selection frame
        selection_frame = ttk.Frame(main_frame)
        selection_frame.pack(fill="x", pady=5)
        
        ttk.Label(selection_frame, text="Available Models:").pack(side="left")
        
        # Model combobox
        self.model_var = tk.StringVar(value=self.selected_model)
        self.model_combobox = ttk.Combobox(
            selection_frame,
            textvariable=self.model_var,
            state="readonly",
            width=30
        )
        self.model_combobox.pack(side="left", padx=(10, 0), fill="x", expand=True)
        self.model_combobox.bind("<<ComboboxSelected>>", self.on_model_selected)
        
        # Refresh button
        self.refresh_button = ttk.Button(
            selection_frame,
            text="🔄 Refresh",
            command=self.refresh_models,
            width=10
        )
        self.refresh_button.pack(side="right", padx=(10, 0))
        
        # Model info frame
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill="x", pady=(10, 0))
        
        self.model_info_label = ttk.Label(
            info_frame,
            text="",
            font=("TkDefaultFont", 8),
            foreground="gray"
        )
        self.model_info_label.pack(anchor="w")
        
        # Status frame
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill="x", pady=(5, 0))
        
        self.status_label = ttk.Label(
            status_frame,
            text="",
            font=("TkDefaultFont", 8)
        )
        self.status_label.pack(anchor="w")
        
        # Test connection button
        test_frame = ttk.Frame(main_frame)
        test_frame.pack(fill="x", pady=(10, 0))
        
        self.test_button = ttk.Button(
            test_frame,
            text="🔗 Test Connection",
            command=self.test_connection
        )
        self.test_button.pack(side="left")
        
        # Connection status
        self.connection_status = ttk.Label(
            test_frame,
            text="",
            font=("TkDefaultFont", 8)
        )
        self.connection_status.pack(side="left", padx=(10, 0))

    def on_provider_changed(self):
        """Handle provider selection change"""
        new_provider = self.provider_var.get()
        if new_provider != getattr(self, 'selected_provider', 'groq'):
            self.selected_provider = new_provider
            self.evaluator.set_provider(new_provider)
            self.refresh_models()

    def refresh_models(self):
        """
        Refresh the list of available models
        """
        self.status_label.config(text="Refreshing models...", foreground="blue")
        self.refresh_button.config(state="disabled")
        
        # Get available models
        success, models = self.evaluator.get_available_models()
        
        if success:
            self.available_models = models
            self.model_combobox['values'] = models
            
            # Set current selection
            if self.selected_model in models:
                self.model_var.set(self.selected_model)
            elif models:
                self.model_var.set(models[0])
                self.selected_model = models[0]
                self.evaluator.set_model(self.selected_model)
            
            self.status_label.config(
                text=f"✓ Found {len(models)} available models",
                foreground="green"
            )
            
            self.update_model_info()
            
        else:
            self.available_models = []
            self.model_combobox['values'] = []
            self.status_label.config(
                text=f"❌ Failed to get models: {models}",
                foreground="red"
            )
            self.model_info_label.config(text="")
        
        self.refresh_button.config(state="normal")
    
    def on_model_selected(self, event=None):
        """
        Handle model selection change
        """
        new_model = self.model_var.get()
        if new_model and new_model != self.selected_model:
            self.selected_model = new_model
            self.evaluator.set_model(new_model)
            self.update_model_info()
            self.status_label.config(
                text=f"✓ Selected model: {new_model}",
                foreground="green"
            )
    
    def update_model_info(self):
        """
        Update model information display
        """
        if self.selected_model:
            # Basic model info
            info_text = f"Current model: {self.selected_model}"
            
            # Add some basic model type detection
            if "llama" in self.selected_model.lower():
                info_text += " (Llama-based model)"
            elif "mistral" in self.selected_model.lower():
                info_text += " (Mistral-based model)"
            elif "cogito" in self.selected_model.lower():
                info_text += " (Cogito model - optimized for analysis)"
            elif "phi" in self.selected_model.lower():
                info_text += " (Phi model - Microsoft)"
            elif "gemma" in self.selected_model.lower():
                info_text += " (Gemma model - Google)"
            
            self.model_info_label.config(text=info_text)
        else:
            self.model_info_label.config(text="No model selected")
    
    def test_connection(self):
        """
        Test connection to Ollama with current model
        """
        self.connection_status.config(text="Testing...", foreground="blue")
        self.test_button.config(state="disabled")

        # Get current provider
        # Get current provider
        provider = getattr(self, 'selected_provider', 'groq')

        if provider == "ollama":
            success, message = self.evaluator.ollama_client.test_connection()
        else:  # groq
            success, message = self.evaluator.groq_client.test_connection()

        if success:
            self.connection_status.config(
                text="✓ Connection successful",
                foreground="green"
            )
        else:
            self.connection_status.config(
                text="❌ Connection failed",
                foreground="red"
            )
            messagebox.showerror("Connection Test Failed", message)
        
        self.test_button.config(state="normal")
    
    def get_selected_model(self):
        """
        Get the currently selected model
        """
        return self.selected_model
    
    def is_model_available(self):
        """
        Check if a model is selected and available
        """
        return bool(self.selected_model and self.available_models)
