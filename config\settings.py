"""
Configuration settings for Talent Hero Evaluator
"""

# Ollama API Configuration
OLLAMA_BASE_URL = "http://localhost:11434"
OLLAMA_MODEL = "cogito:3b"

# Application Settings
APP_TITLE = "Talent Hero Evaluator"
APP_VERSION = "1.0.0"
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800

# Evaluation Parameters
DEFAULT_EVALUATION_PARAMETERS = [
    "Skills Match",
    "Experience Relevance", 
    "Education Fit",
    "Technical Competency",
    "Communication Skills",
    "Leadership Experience",
    "Industry Knowledge",
    "Cultural Fit",
    "Career Progression",
    "Certifications & Training"
]

# File Settings
SUPPORTED_FILE_TYPES = [
    ("Document files", "*.pdf;*.docx;*.doc"),
    ("PDF files", "*.pdf"),
    ("Word files", "*.docx;*.doc"),
    ("All files", "*.*")
]
MAX_FILE_SIZE_MB = 10

# Scoring
MIN_SCORE = 0
MAX_SCORE = 10

