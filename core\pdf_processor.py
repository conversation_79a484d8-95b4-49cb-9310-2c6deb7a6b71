"""
PDF processing utilities for extracting text from PDF files
"""
import PyPDF2
from pathlib import Path


class PDFProcessor:
    """
    Class to handle PDF text extraction
    """
    
    def __init__(self):
        pass
    
    def extract_text_from_pdf(self, pdf_path):
        """
        Extract text content from a PDF file
        
        Args:
            pdf_path (str): Path to the PDF file
            
        Returns:
            tuple: (success: bool, content: str or error_message: str)
        """
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Check if PDF is encrypted
                if pdf_reader.is_encrypted:
                    return False, "PDF is encrypted and cannot be processed"
                
                text_content = ""
                
                # Extract text from all pages
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text_content += page.extract_text() + "\n"
                
                # Clean up the text
                text_content = self._clean_text(text_content)
                
                if not text_content.strip():
                    return False, "No text content found in PDF"
                
                return True, text_content
                
        except Exception as e:
            return False, f"Error processing PDF: {str(e)}"
    
    def _clean_text(self, text):
        """
        Clean and normalize extracted text
        
        Args:
            text (str): Raw extracted text
            
        Returns:
            str: Cleaned text
        """
        # Remove excessive whitespace
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line:  # Only keep non-empty lines
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def get_pdf_info(self, pdf_path):
        """
        Get basic information about the PDF
        
        Args:
            pdf_path (str): Path to the PDF file
            
        Returns:
            dict: PDF information
        """
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                info = {
                    'num_pages': len(pdf_reader.pages),
                    'is_encrypted': pdf_reader.is_encrypted,
                    'file_size_mb': Path(pdf_path).stat().st_size / (1024 * 1024)
                }
                
                # Try to get metadata
                if pdf_reader.metadata:
                    info['title'] = pdf_reader.metadata.get('/Title', 'Unknown')
                    info['author'] = pdf_reader.metadata.get('/Author', 'Unknown')
                else:
                    info['title'] = 'Unknown'
                    info['author'] = 'Unknown'
                
                return info
                
        except Exception as e:
            return {'error': str(e)}
