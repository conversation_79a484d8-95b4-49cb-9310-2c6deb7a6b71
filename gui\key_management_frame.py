"""
Key management frame for Groq API keys
"""
import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from pathlib import Path


class KeyManagementFrame(ttk.Frame):
    """
    Frame for managing Groq API keys
    """
    
    def __init__(self, parent, groq_client):
        super().__init__(parent)
        self.groq_client = groq_client
        self.keys_file = Path("config/groq_keys.json")
        
        self.create_widgets()
        self.load_saved_keys()
    
    def create_widgets(self):
        """
        Create and layout widgets
        """
        # Main frame
        main_frame = ttk.LabelFrame(self, text="Groq API Key Management", padding="10")
        main_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Instructions
        instructions = ttk.Label(
            main_frame,
            text="Add your Groq API keys to avoid rate limits. Keys are rotated automatically.",
            font=("TkDefaultFont", 9)
        )
        instructions.pack(anchor="w", pady=(0, 10))
        
        # Add key frame
        add_frame = ttk.LabelFrame(main_frame, text="Add New Key", padding="5")
        add_frame.pack(fill="x", pady=(0, 10))
        
        # Key entry
        entry_frame = ttk.Frame(add_frame)
        entry_frame.pack(fill="x", pady=5)
        
        ttk.Label(entry_frame, text="API Key:").pack(side="left")
        
        self.key_entry = ttk.Entry(entry_frame, width=50, show="*")
        self.key_entry.pack(side="left", padx=(5, 0), fill="x", expand=True)
        
        self.add_button = ttk.Button(
            entry_frame,
            text="Add Key",
            command=self.add_key
        )
        self.add_button.pack(side="right", padx=(5, 0))
        
        # Show/Hide key button
        self.show_key_var = tk.BooleanVar()
        self.show_key_check = ttk.Checkbutton(
            add_frame,
            text="Show key while typing",
            variable=self.show_key_var,
            command=self.toggle_key_visibility
        )
        self.show_key_check.pack(anchor="w")
        
        # Existing keys frame
        keys_frame = ttk.LabelFrame(main_frame, text="Your API Keys", padding="5")
        keys_frame.pack(fill="both", expand=True, pady=(0, 10))
        
        # Keys listbox with scrollbar
        list_frame = ttk.Frame(keys_frame)
        list_frame.pack(fill="both", expand=True)
        
        self.keys_listbox = tk.Listbox(list_frame, height=6)
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.keys_listbox.yview)
        self.keys_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.keys_listbox.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Key management buttons
        button_frame = ttk.Frame(keys_frame)
        button_frame.pack(fill="x", pady=(5, 0))
        
        self.remove_button = ttk.Button(
            button_frame,
            text="Remove Selected",
            command=self.remove_key
        )
        self.remove_button.pack(side="left")
        
        self.test_button = ttk.Button(
            button_frame,
            text="Test Connection",
            command=self.test_connection
        )
        self.test_button.pack(side="left", padx=(5, 0))
        
        # Status frame
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill="x")
        
        self.status_label = ttk.Label(
            status_frame,
            text="Ready to manage API keys",
            font=("TkDefaultFont", 9)
        )
        self.status_label.pack(side="left")
        
        # Key count info
        self.key_count_label = ttk.Label(
            status_frame,
            text="",
            font=("TkDefaultFont", 9),
            foreground="blue"
        )
        self.key_count_label.pack(side="right")
        
        # Bind Enter key to add key
        self.key_entry.bind("<Return>", lambda e: self.add_key())
    
    def toggle_key_visibility(self):
        """
        Toggle visibility of API key in entry field
        """
        if self.show_key_var.get():
            self.key_entry.config(show="")
        else:
            self.key_entry.config(show="*")
    
    def add_key(self):
        """
        Add a new API key
        """
        key = self.key_entry.get().strip()
        
        if not key:
            messagebox.showwarning("Empty Key", "Please enter an API key.")
            return
        
        if not key.startswith("gsk_"):
            messagebox.showwarning("Invalid Key", "Groq API keys should start with 'gsk_'.")
            return
        
        if self.groq_client.add_user_key(key):
            self.key_entry.delete(0, tk.END)
            self.refresh_keys_list()
            self.save_keys()
            self.status_label.config(text="✓ API key added successfully", foreground="green")
        else:
            messagebox.showwarning("Duplicate Key", "This API key is already added.")
    
    def remove_key(self):
        """
        Remove selected API key
        """
        selection = self.keys_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select a key to remove.")
            return
        
        index = selection[0]
        user_keys = self.groq_client.get_user_keys()
        
        if index < len(user_keys):
            key_to_remove = user_keys[index]
            
            # Confirm removal
            result = messagebox.askyesno(
                "Confirm Removal",
                f"Remove API key ending in ...{key_to_remove[-8:]}?"
            )
            
            if result:
                if self.groq_client.remove_user_key(key_to_remove):
                    self.refresh_keys_list()
                    self.save_keys()
                    self.status_label.config(text="✓ API key removed", foreground="green")
    
    def refresh_keys_list(self):
        """
        Refresh the keys listbox
        """
        self.keys_listbox.delete(0, tk.END)
        
        user_keys = self.groq_client.get_user_keys()
        for key in user_keys:
            # Show only last 8 characters for security
            masked_key = f"gsk_...{key[-8:]}"
            self.keys_listbox.insert(tk.END, masked_key)
        
        # Update key count
        total_keys = len(self.groq_client.all_keys)
        user_key_count = len(user_keys)
        self.key_count_label.config(
            text=f"Total keys: {total_keys} (Your keys: {user_key_count})"
        )
    
    def test_connection(self):
        """
        Test connection to Groq API
        """
        self.status_label.config(text="Testing connection...", foreground="blue")
        self.test_button.config(state="disabled")
        
        success, message = self.groq_client.test_connection()
        
        if success:
            self.status_label.config(text="✓ Connection successful", foreground="green")
        else:
            self.status_label.config(text="❌ Connection failed", foreground="red")
            messagebox.showerror("Connection Test Failed", message)
        
        self.test_button.config(state="normal")
    
    def save_keys(self):
        """
        Save user keys to file
        """
        try:
            # Ensure config directory exists
            self.keys_file.parent.mkdir(exist_ok=True)
            
            user_keys = self.groq_client.get_user_keys()
            with open(self.keys_file, 'w') as f:
                json.dump({"user_keys": user_keys}, f, indent=2)
                
        except Exception as e:
            messagebox.showerror("Save Error", f"Failed to save keys: {str(e)}")
    
    def load_saved_keys(self):
        """
        Load saved user keys from file
        """
        try:
            if self.keys_file.exists():
                with open(self.keys_file, 'r') as f:
                    data = json.load(f)
                    user_keys = data.get("user_keys", [])
                    
                    for key in user_keys:
                        self.groq_client.add_user_key(key)
                    
                    self.refresh_keys_list()
                    
                    if user_keys:
                        self.status_label.config(
                            text=f"✓ Loaded {len(user_keys)} saved keys",
                            foreground="green"
                        )
            else:
                self.refresh_keys_list()
                
        except Exception as e:
            messagebox.showerror("Load Error", f"Failed to load saved keys: {str(e)}")
            self.refresh_keys_list()
