"""
Talent Hero Evaluator - Main Application Entry Point

A Tkinter application for evaluating resumes against job descriptions using AI.
"""
import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from gui.main_window import MainWindow
    from config.settings import APP_TITLE
except ImportError as e:
    print(f"Import error: {e}")
    print("Please make sure all required modules are installed.")
    sys.exit(1)


def check_dependencies():
    """
    Check if all required dependencies are available
    """
    missing_deps = []
    
    try:
        import PyPDF2
    except ImportError:
        missing_deps.append("PyPDF2")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        from PIL import Image
    except ImportError:
        missing_deps.append("Pillow")
    
    if missing_deps:
        error_msg = f"""
Missing required dependencies: {', '.join(missing_deps)}

Please install them using:
pip install {' '.join(missing_deps)}

Or install all dependencies with:
pip install -r requirements.txt
        """
        
        # Try to show GUI error if tkinter is available
        try:
            root = tk.Tk()
            root.withdraw()  # Hide main window
            messagebox.showerror("Missing Dependencies", error_msg.strip())
            root.destroy()
        except:
            print(error_msg)
        
        return False
    
    return True


def main():
    """
    Main application entry point
    """
    print(f"Starting {APP_TITLE}...")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    try:
        # Create and run the application
        app = MainWindow()
        
        print("Application initialized successfully.")
        print("Make sure Ollama is running on localhost:11434 with the cogito:3b model.")
        
        app.run()
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user.")
        sys.exit(0)
        
    except Exception as e:
        error_msg = f"An unexpected error occurred: {str(e)}"
        print(error_msg)
        
        # Try to show GUI error
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Application Error", error_msg)
            root.destroy()
        except:
            pass
        
        sys.exit(1)


if __name__ == "__main__":
    main()
