"""
File upload frame for job description and resume files
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from utils.file_utils import validate_document_file
from config.settings import SUPPORTED_FILE_TYPES


class FileUploadFrame(ttk.Frame):
    """
    Frame for handling file uploads
    """
    
    def __init__(self, parent):
        super().__init__(parent)
        self.job_description_path = None
        self.resume_paths = []
        
        self.create_widgets()
    
    def create_widgets(self):
        """
        Create and layout widgets
        """
        # Job Description Section
        job_desc_frame = ttk.LabelFrame(self, text="Job Description", padding="10")
        job_desc_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(job_desc_frame, text="Upload a single PDF job description:").pack(anchor="w")
        
        job_desc_button_frame = ttk.Frame(job_desc_frame)
        job_desc_button_frame.pack(fill="x", pady=5)
        
        self.job_desc_button = ttk.Button(
            job_desc_button_frame, 
            text="Select Job Description PDF",
            command=self.select_job_description
        )
        self.job_desc_button.pack(side="left")
        
        self.job_desc_label = ttk.Label(job_desc_button_frame, text="No file selected")
        self.job_desc_label.pack(side="left", padx=(10, 0))
        
        # Resume Files Section
        resume_frame = ttk.LabelFrame(self, text="Resume Files", padding="10")
        resume_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        ttk.Label(resume_frame, text="Upload multiple PDF resume files:").pack(anchor="w")
        
        resume_button_frame = ttk.Frame(resume_frame)
        resume_button_frame.pack(fill="x", pady=5)
        
        self.resume_button = ttk.Button(
            resume_button_frame,
            text="Select Resume PDFs",
            command=self.select_resumes
        )
        self.resume_button.pack(side="left")
        
        self.clear_resumes_button = ttk.Button(
            resume_button_frame,
            text="Clear All",
            command=self.clear_resumes
        )
        self.clear_resumes_button.pack(side="left", padx=(10, 0))
        
        # Resume list
        list_frame = ttk.Frame(resume_frame)
        list_frame.pack(fill="both", expand=True, pady=5)
        
        # Scrollable listbox for resumes
        self.resume_listbox = tk.Listbox(list_frame, height=8)
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.resume_listbox.yview)
        self.resume_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.resume_listbox.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Resume count label
        self.resume_count_label = ttk.Label(resume_frame, text="0 resumes selected")
        self.resume_count_label.pack(anchor="w", pady=5)
    
    def select_job_description(self):
        """
        Select job description document file
        """
        file_path = filedialog.askopenfilename(
            title="Select Job Description (PDF/Word)",
            filetypes=SUPPORTED_FILE_TYPES
        )
        
        if file_path:
            # Validate the file
            is_valid, message = validate_document_file(file_path)
            
            if is_valid:
                self.job_description_path = file_path
                filename = os.path.basename(file_path)
                self.job_desc_label.config(text=f"✓ {filename}")
                self.job_desc_label.config(foreground="green")
            else:
                messagebox.showerror("Invalid File", message)
                self.job_desc_label.config(text="No file selected")
                self.job_desc_label.config(foreground="black")
    
    def select_resumes(self):
        """
        Select multiple resume document files
        """
        file_paths = filedialog.askopenfilenames(
            title="Select Resume Documents (PDF/Word)",
            filetypes=SUPPORTED_FILE_TYPES
        )
        
        if file_paths:
            valid_files = []
            invalid_files = []
            
            for file_path in file_paths:
                is_valid, message = validate_document_file(file_path)
                if is_valid:
                    valid_files.append(file_path)
                else:
                    invalid_files.append((os.path.basename(file_path), message))
            
            # Add valid files to the list
            for file_path in valid_files:
                if file_path not in self.resume_paths:
                    self.resume_paths.append(file_path)
                    self.resume_listbox.insert(tk.END, os.path.basename(file_path))
            
            # Show warning for invalid files
            if invalid_files:
                invalid_list = "\n".join([f"• {name}: {msg}" for name, msg in invalid_files])
                messagebox.showwarning(
                    "Some Files Invalid", 
                    f"The following files were skipped:\n\n{invalid_list}"
                )
            
            self.update_resume_count()
    
    def clear_resumes(self):
        """
        Clear all selected resume files
        """
        self.resume_paths.clear()
        self.resume_listbox.delete(0, tk.END)
        self.update_resume_count()
    
    def update_resume_count(self):
        """
        Update the resume count label
        """
        count = len(self.resume_paths)
        self.resume_count_label.config(text=f"{count} resume{'s' if count != 1 else ''} selected")
    
    def get_job_description_path(self):
        """
        Get the selected job description file path
        """
        return self.job_description_path
    
    def get_resume_paths(self):
        """
        Get the list of selected resume file paths
        """
        return self.resume_paths.copy()
    
    def is_ready_for_evaluation(self):
        """
        Check if files are ready for evaluation
        """
        return self.job_description_path is not None and len(self.resume_paths) > 0

