"""
Ollama API client for resume evaluation
"""
import requests
import json
from config.settings import OLLAMA_BASE_URL, OLLAMA_MODEL


class OllamaClient:
    """
    Client for interacting with Ollama API
    """
    
    def __init__(self, model=None):
        self.base_url = OLLAMA_BASE_URL
        self.model = model or OLLAMA_MODEL
    
    def test_connection(self):
        """
        Test if Ollama is running and accessible
        
        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [model['name'] for model in models]
                
                if self.model in model_names:
                    return True, f"Connected to Ollama. Model '{self.model}' is available."
                else:
                    return False, f"Model '{self.model}' not found. Available models: {model_names}"
            else:
                return False, f"Ollama API returned status code: {response.status_code}"
                
        except requests.exceptions.ConnectionError:
            return False, "Cannot connect to Ollama. Make sure it's running on localhost:11434"
        except Exception as e:
            return False, f"Error connecting to Ollama: {str(e)}"

    def get_available_models(self):
        """
        Get list of available models from Ollama

        Returns:
            tuple: (success: bool, models: list or error_message: str)
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models_data = response.json().get('models', [])
                model_names = [model['name'] for model in models_data]
                return True, model_names
            else:
                return False, f"API returned status code: {response.status_code}"

        except requests.exceptions.ConnectionError:
            return False, "Cannot connect to Ollama. Make sure it's running on localhost:11434"
        except Exception as e:
            return False, f"Error getting models: {str(e)}"

    def set_model(self, model_name):
        """
        Set the model to use for evaluation

        Args:
            model_name (str): Name of the model to use
        """
        self.model = model_name
    
    def evaluate_resume(self, job_description, resume_text, parameters):
        """
        Evaluate a resume against job description using specified parameters
        
        Args:
            job_description (str): Job description text
            resume_text (str): Resume text content
            parameters (list): List of evaluation parameters
            
        Returns:
            tuple: (success: bool, scores: dict or error_message: str)
        """
        try:
            # Create evaluation prompt
            prompt = self._create_evaluation_prompt(job_description, resume_text, parameters)
            
            # Make API call to Ollama
            response = requests.post(
                f"{self.base_url}/api/generate",
                json={
                    "model": self.model,
                    "prompt": prompt,
                    "stream": False
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                evaluation_text = result.get('response', '')
                
                # Parse the evaluation response
                scores = self._parse_evaluation_response(evaluation_text, parameters)
                return True, scores
            else:
                return False, f"API request failed with status: {response.status_code}"
                
        except Exception as e:
            return False, f"Error during evaluation: {str(e)}"
    
    def _create_evaluation_prompt(self, job_description, resume_text, parameters):
        """
        Create evaluation prompt for the AI model
        """
        parameters_str = ", ".join(parameters)
        
        prompt = f"""
You are an expert HR recruiter evaluating a resume against a job description.

JOB DESCRIPTION:
{job_description}

RESUME:
{resume_text}

Please evaluate this resume against the job description based on the following parameters: {parameters_str}

For each parameter, provide a score from 0 to 10 (where 0 is very poor and 10 is excellent).

Respond in the following JSON format:
{{
    "scores": {{
        "{parameters[0]}": score,
        "{parameters[1]}": score,
        ...
    }},
    "overall_comments": "Brief overall assessment"
}}

Be objective and provide specific reasoning for your scores.
"""
        return prompt
    
    def _parse_evaluation_response(self, response_text, parameters):
        """
        Parse the AI model's evaluation response
        
        Returns:
            dict: Parsed scores and comments
        """
        try:
            # Try to extract JSON from the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                parsed_data = json.loads(json_str)
                
                # Validate and clean scores
                scores = {}
                for param in parameters:
                    score = parsed_data.get('scores', {}).get(param, 0)
                    # Ensure score is between 0 and 10
                    scores[param] = max(0, min(10, float(score)))
                
                return {
                    'scores': scores,
                    'overall_comments': parsed_data.get('overall_comments', 'No comments provided'),
                    'average_score': sum(scores.values()) / len(scores) if scores else 0
                }
            else:
                # Fallback: assign default scores if JSON parsing fails
                return {
                    'scores': {param: 5.0 for param in parameters},
                    'overall_comments': 'Failed to parse evaluation response',
                    'average_score': 5.0
                }
                
        except Exception as e:
            # Fallback for parsing errors
            return {
                'scores': {param: 5.0 for param in parameters},
                'overall_comments': f'Error parsing response: {str(e)}',
                'average_score': 5.0
            }
