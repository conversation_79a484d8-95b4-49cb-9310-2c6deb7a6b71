"""
File utility functions for Talent Hero Evaluator
"""
import os
import shutil
from pathlib import Path
from tkinter import messagebox


def validate_document_file(file_path):
    """
    Validate if the file is a valid PDF or Word document
    """
    if not os.path.exists(file_path):
        return False, "File does not exist"
    
    file_ext = file_path.lower()
    if not (file_ext.endswith('.pdf') or file_ext.endswith('.docx') or file_ext.endswith('.doc')):
        return False, "File must be a PDF or Word document (.pdf, .docx, .doc)"
    
    # Check file size (max 10MB)
    file_size = os.path.getsize(file_path) / (1024 * 1024)  # Convert to MB
    if file_size > 10:
        return False, f"File size ({file_size:.1f}MB) exceeds 10MB limit"
    
    return True, "Valid document file"


def create_downloads_folder():
    """
    Create downloads folder if it doesn't exist
    """
    downloads_path = Path("downloads")
    downloads_path.mkdir(exist_ok=True)
    return downloads_path


def copy_file_to_downloads(source_path, filename=None):
    """
    Copy file to downloads folder
    """
    try:
        downloads_path = create_downloads_folder()
        
        if filename is None:
            filename = os.path.basename(source_path)
        
        destination = downloads_path / filename
        shutil.copy2(source_path, destination)
        
        return True, str(destination)
    except Exception as e:
        return False, str(e)


def get_file_size_mb(file_path):
    """
    Get file size in MB
    """
    return os.path.getsize(file_path) / (1024 * 1024)


def sanitize_filename(filename):
    """
    Sanitize filename for safe saving
    """
    if not filename:
        return "file"

    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    sanitized = str(filename)
    for char in invalid_chars:
        sanitized = sanitized.replace(char, '_')

    # Remove leading/trailing whitespace and dots
    sanitized = sanitized.strip(' .')

    # Ensure it's not empty after sanitization
    if not sanitized:
        return "file"

    return sanitized

